server:
  port: 8086
  servlet:
    context-path: /wms/web
    encoding:
      enabled: true
      charset: UTF-8
      force: true

springfox:
  documentation:
    enabled: false

spring:
  main:
    allow-bean-definition-overriding: true
    # allow-circular-references: true
    # lazy-initialization: true
  web:
    resources:
      static-locations: classpath:/META-INF/resources/,classpath:/resources/,classpath:/static/,classpath:/public/,classpath:/templates/
  mvc:
    static-path-pattern: /**
    view:
      prefix: /
      suffix: .html
  datasource:
    dynamic:
      #设置默认的数据源或者数据源组,默认值即为master
      primary: write
      #设置严格模式,默认false不启动. 启动后在未匹配到指定数据源时候会抛出异常,不启动则使用默认数据源.
      strict: false
      datasource:
        write:
          url: ***************************************************************************************************************************************************************************************
          username: root
          password: '!@#QWE123qwe'
          driver-class-name: com.mysql.cj.jdbc.Driver
        read:
          url: ***************************************************************************************************************************************************************************************
          username: root
          password: '!@#QWE123qwe'
          driver-class-name: com.mysql.cj.jdbc.Driver
  #        ums:
  #          #UMS统一认证平台数据源配置
  #          url: jdbc:oracle:thin:@************:1521:orcl
  #          username: ums
  #          password: 'ums'
  #          driver-class-name: oracle.jdbc.driver.OracleDriver
  redis:
    databases:
      base: 4
      business: 5
    #host: sdw-dev-redis.swf-sco-db.svc.cluster.local
    host: SSWMS.db.ad01.sec.com
    port: 26379
    password: 'redis!@#QWE123qwe'
    # 连接超时timeout属性，单位： 毫秒
    timeout: 30000
    lettuce:
      # 在关闭客户端连接之前等待任务处理完成的最长时间，在这之后，无论任务是否执行完成，都会被执行器关闭，默认100ms
      shutdown-timeout: 100
      pool:
        # 连接池最大连接数（使用负值表示没有限制）
        max-active: 100
        # 连接池中的最大空闲连接
        max-idle: 20
        # 连接池中的最小空闲连接
        min-idle: 2
        # max-wait属性，单位： 毫秒
        max-wait: 1000000
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  mail:
    host: smtp.163.com #邮箱通道   注意企业邮箱和个人邮箱不同
    port: 25
    username: 'none'   #发送的邮箱
    password: 'none'  #授权码
    default-encoding: utf-8
  activiti:
    check-process-definitions: false
    # 检测身份信息表是否存在
    db-identity-used: false
    async-executor-activate: false
  quartz:
    properties:
      org:
        quartz:
          jobStore:
            class: org.springframework.scheduling.quartz.LocalDataSourceJobStore

mybatis-plus:
  global-config:
    banner: off
    db-config:
      logic-delete-field: "isDelete"
      logic-delete-value: "null" # 逻辑已删除值(默认为 null)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)
      id-type: input
  mapper-locations: classpath*:/**/xml/*.xml
  type-aliases-package: com.inossem.wms.common.model;com.inossem.wms.workflow.base.model
  typeEnumsPackage: com.inossem.wms.common.enums
  configuration:
    # 配置项：开启下划线到驼峰的自动转换. 作用：将数据库字段根据驼峰规则自动注入到对象属性。
    map-underscore-to-camel-case: true
    #sql日志，暂选为原mybatis自带sql日志，方便调试。
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    #如果查询结果中包含空值的列，则 MyBatis 在映射的时候，不会映射这个字段
    call-setters-on-nulls: false

# wms 自定义配置
wms:
  application-version:
  application-build-timestamp:
  # InStock系统配置
  system:
    file-path: /home/<USER>/data/ins_file/
    img-path: /home/<USER>/data/ins_img/
    app-upgrade-file-path: /home/<USER>/data/app_upgrade_file/
    cache-refresh: true
    base-url: 'none'
    ledger-file-path: /home/<USER>/data/ins_ledger_file/
    wms-username: 'none'
    wms-password: 'none'
  # 短信发送 - 使用网易云信
  sms:
    app-key: 'none'
    app-secret: 'none'
    server-url: 'none'
  mobile-device:
    pda:
      app-id: __UNI__13EB62F
  # ERP 相关配置
  erp:
    # 同步ERP接口数据
    data-sync-enabled: true
    # rest方式访问erp的接口地址url
    rest-api-url: http://webapp08.shanghai-electric.com/dce-api/sjram-service/api/do/SSWMS/P12/612/
    username: 'none'
    password: 'none'
    secret: 9953a46894d448389efa2a3ba06589e4
  # BI 相关配置
  bi:
    # rest方式访问erp的接口地址url
    rest-api-url: http://webqas08.shanghai-electric.com/dce-api/sjram-service/api/do/SSWMS/D42/142/
    username: 'none'
    password: 'none'
    secret: 8c2c349cf2e44e8ca2f44ba72a6abcee
  oil:
    enabled: true
    url: https://thardm.apps.paas.se-unicloud.com/web/dashboard/metaApi/stockIn
    appId: 685b833faa146c5a32114d6d
    appSecret: KaB23UE00m9q0RRhesCSvxOoPjvm9L1CwBgeQuCm60LFiWK6WZ0FEJNKhibBSybA
    username: wms-prod-username
    password: wms-prod-password
  oa:
    # 同步ERP接口数据
    data-sync-enabled: true
    # rest方式访问erp的接口地址url
    #    rest-api-url: http://uatapi.shanghai-electric.com/apigatewaytest/ONETASK/dce-api/soap-proxy/do/SSWMS/sysNotifyTodoWebService?X-Sec-App-Auth-Key=Db3S72tVSn2YeSw
    rest-api-url: http://xapi.db.ad01.sec.com/apigateway/ONETASK/dce-api/soap-proxy/do/SSWMS/sysNotifyTodoWebService?X-Sec-App-Auth-Key=dfd6c4c9ea42d8b817c2d22c5f409393
    username: SSWMS2ONETASK
    password: wG5nR3cF6l
    app-name: SSWMS
    model-name: SSWMS
    model-id: dfd6c4c9ea42d8b817c2d22c5f409393
  #领导
  leader:
    #华信领导
    hx-leader: '10100212'
    #能殷领导
    ny-leader: '10100714'
  # UMS 相关配置
  ums:
    url: 'none'
    urlOther: 'none'
  dts:
    sync: false
    ftp-path: 'none'
    ftp-port: 0000
    username: 'none'
    password: 'none'
    directory: 'none'
  # 业务自定义配置
  biz-domain:
    # 通用业务配置
    common:
      # 附件
      attachment-enabled: true
      # 操作日志启用
      operation-log-enabled: true
      # 标签打印机启用
      label-printer-enabled: true
    # 仓库作业配置
    task:
      # 上架策略启用
      load-strategy-enabled: true
      # 下架作业启用
      unload-strategy-enabled: false
  srm:
    app-id: HXZY
    app-secret: fec151a7d2124d2c9f6facc24434a385
    # 寻源竞价文件上传
    attachment-url: https://ssc-platform.shanghai-electric.com/ssc-bidding/api/v1/attachment
    # 新增寻源竞价采购项目
    createMain-url: https://ssc-platform.shanghai-electric.com/ssc-bidding/api/v1/createMain
    # 回传srm 付款信息
    createPaymentPlan-url: https://ssc-platform.shanghai-electric.com/ssc-structured-protocol/api/v1/createPaymentPlan
    # 同步供应商信息
    syncSupplier-url: https://ssc-platform.shanghai-electric.com/ssc-enterprise-purchaser/api/v1/external/access
rocket:
  #namesrv-addr: sdw-dev-mqnamesrv.swf-sco-mq.svc.cluster.local:9876
  namesrv-addr: SSWMS.db.ad01.sec.com:9876
  #  namesrv-addr: 127.0.0.1:9876
  # 生产者分组
  producer-group: instock-v3-hx
  # 消费者分组
  consumer-group: instock-v3-hx
  # 生产回调消息的生产者分组
  producer-group-ack: instock-ack-hx
  # 生产回调消息的消费者分组
  consumer-group-ack: instock-ack-hx
  # 订阅主题
  topic: instock-topic-hx
  # 标签
  tages: instock3.0-hx
  # 客户端限制的消息大小，超过报错，同时服务端也会限制，所以需要跟服务端配合使用 默认4M
  max-message-size: 1024 * 1024 * 256

# token配置
token:
  access_token:
    # 令牌标识
    header: Authorization
    #  过期时间 单位：分钟，240分钟(4小时) 一天1440  2880000(200年)
    expiration: 480
    #  签名
    sign_key: INOSSEM_INSTOCK
  # 刷新令牌标识
  refresh_token:
    # 令牌标识
    header: refreshToken
    #  过期时间 单位：分钟，240分钟(4小时) 一天1440 三天4230 一周10080  2880000(200年)
    expiration: 10080
    #  签名
    sign_key: INOSSEM_INSTOCK_WMS

ums:
  #UMS统一认证平台数据源配置
  url: 'none'
  username: 'none'
  password: 'none'
  driver-class-name: 'none'
  table: 'none'
ihn:
  corpid: 'none'
  corpsecret: 'none'
  get_token_url: 'none'
  get_ticket_url: 'none'
  get_user_info_url: 'none'
sso:
  ssoIp: 'none'
  ssoCn: 'none'
  app-code: SSWMS
  gateway-token: dfd6c4c9ea42d8b817c2d22c5f409393
  interface-username: SSWMS2AUTH
  interface-password: wG5nR3cF6l
  auth-url:  http://xapi.db.ad01.sec.com/apigateway/NAUTHM/NAUTHM/services/api/account/login?X-Sec-App-Auth-Key=dfd6c4c9ea42d8b817c2d22c5f409393
  redirect-url: http://earth-manage.shanghai-electric.com/wms/web/html/callback
  out-redirect-url: https://earth.shanghai-electric.com/wms/web/html/callback
aes-key: wms4567890123456
