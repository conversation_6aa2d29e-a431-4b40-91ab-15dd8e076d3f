package com.inossem.wms.bizbasis.sap.restful.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.inossem.wms.bizbasis.oa.service.datawrap.OaTodoDataWrap;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.oa.entity.OaTodo;
import com.inossem.wms.common.util.UtilAES;
import com.inossem.wms.common.util.UtilConst;
import com.inossem.wms.common.util.UtilDate;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.UtilSequence;
import com.inossem.wms.common.util.sap.SapApiCallProxy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 华信OA接口服务类
 * 负责与OA系统进行待办事项的交互，包括发送待办、完成待办和删除待办
 * 
 * <p>主要功能:
 * <ul>
 *   <li>发送待办到OA系统</li>
 *   <li>完成OA系统中的待办</li>
 *   <li>删除OA系统中的待办</li>
 * </ul>
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-13
 */
@Service
@Slf4j
public class HXOaIntegerfaceService {

    @Autowired
    protected SapApiCallProxy sapApiCallProxy;

    @Autowired
    protected OaTodoDataWrap oaTodoDataWrap;

    @Value("${sso.redirect-url}")
    private String redirectUrl;

    @Value("${aes-key}")
    private String aesKey;

    // OA接口动作类型常量
    private static final String ACTION_SEND_TODO = "sendTodo";
    private static final String ACTION_COMPLETE_TODO = "setTodoDone";
    private static final String ACTION_DELETE_TODO = "deleteTodo";

    // OA操作类型常量
    private static final int OPT_TYPE_COMPLETE = 1;
    private static final int OPT_TYPE_DELETE = 1;
    private static final int TODO_TYPE_APPROVAL = 1;
    private static final int TODO_TYPE_NOTICE = 2;

    // OA待办发送类型常量 1:审批待办(key不会重复) 2:普通待办(key手动生成) 3:抄送(key手动生成,通知类待办)
    public static final int SEND_TYPE_APPROVAL = 1;
    public static final int SEND_TYPE_DEFAULT = 2;
    public static final int SEND_TYPE_NOTICE = 3;

    /**
     * 获取OA接口调用的通用请求头参数
     * 包含服务地址、认证信息、方法名、输入输出参数等基础配置
     *
     * @param action 接口动作类型
     * @return JsonObject 包含通用参数的JSON对象
     */
    private JsonObject getHeadParams(String action) {
        return Optional.of(new JsonObject())
            .map(params -> {
                // 服务地址
                params.addProperty("wsdl", "");
                
                // 认证信息
                JsonObject auth = new JsonObject();
                auth.addProperty("serviceAccount", UtilConst.getInstance().getOaUserName());
                auth.addProperty("servicePassword", UtilConst.getInstance().getOaPassword());
                params.add("auth", auth);

                // 设置方法名
                params.addProperty("action", action);

                // 构建input参数
                JsonObject input = new JsonObject();
                input.addProperty("isXml", false);
                params.add("input", input);
                
                // 构建output参数
                JsonObject output = new JsonObject();
                output.addProperty("isXml", false);
                output.addProperty("return", "return");
                params.add("output", output);

                return params;
            })
            .orElseGet(JsonObject::new);
    }

    /**
     * 构建待办参数
     * 根据不同的动作类型构建对应的参数结构
     *
     * @param subject 标题(仅发送待办时需要)
     * @param link 链接(仅发送待办时需要)
     * @param key 待办唯一标识
     * @param userList 用户列表
     * @param action 动作类型
     * @return JsonObject 待办参数对象
     */
    private JsonObject buildSendTodoParams(String subject, String link, String key, List<String> userList, String action,String receiptCode, int todoType) {
        JsonObject dataObject = new JsonObject();
        dataObject.addProperty("appName", UtilConst.getInstance().getOaAppName());
        dataObject.addProperty("modelName", UtilConst.getInstance().getOaModelName());
        dataObject.addProperty("modelId", UtilConst.getInstance().getOaModelId());
        dataObject.addProperty("key", key);
        dataObject.addProperty("type", todoType);
        dataObject.addProperty("param1", receiptCode);
        dataObject.addProperty("param2", "");

        // 构建目标用户数组
        JsonArray targets = new JsonArray();
        userList.forEach(user -> {
            JsonObject userObj = new JsonObject();
            userObj.addProperty("LoginName", user);
            targets.add(userObj);
        });
        dataObject.addProperty("targets", targets.toString());

        // 根据动作类型设置特定参数
        switch (action) {
            case ACTION_SEND_TODO:
                dataObject.addProperty("subject", subject);
                dataObject.addProperty("link", link);
                dataObject.addProperty("createTime", UtilDate.getStringDateTimeForDate(new Date()));
                break;
            case ACTION_COMPLETE_TODO:
                dataObject.addProperty("optType", OPT_TYPE_COMPLETE);
                break;
            case ACTION_DELETE_TODO:
                dataObject.addProperty("optType", OPT_TYPE_DELETE);
                break;
            default:
                log.warn("未知的动作类型: {}", action);
        }
        
        return dataObject;
    }

    /**
     * 统一处理OA返回结果
     * 检查接口调用状态，非成功状态抛出异常
     *
     * @param response OA接口返回的JSON响应
     * @param operationType 操作类型，用于日志记录
     * @throws WmsException 当接口调用失败时抛出异常
     */
    private void handleOaResponse(JsonObject response, String operationType) {
        Optional.ofNullable(response)
            .filter(resp -> !resp.get("success").getAsBoolean())
            .ifPresent(resp -> {
                String errorMsg = UtilObject.getStringOrEmpty(resp.get("message").getAsString());
                log.error("OA接口调用失败 - 操作类型: {}, 错误信息: {}", operationType, errorMsg);
                throw new WmsException(EnumReturnMsg.RETURN_CODE_INTERFACE_CALL_FAILURE, errorMsg);
            });
    }

    /**
     * 数据库保存OA待办
     * 当待办办结或删除时，会同步删除数据库中的待办信息
     *
     * @param subject 待办标题
     * @param todoKey 待办唯一标识
     * @param todoId 审批流程id/单据主键id
     * @param userList 目标用户列表
     * @param params OA接口参数对象
     * @param returnObject OA接口返回结果对象
     */
    private void saveOaTodo(String subject, String todoKey, String todoId, String receiptCode, List<String> userList, JsonObject params, JsonObject returnObject) {
        List<OaTodo> oaTodoList = new ArrayList<>();
        for (String userCode : userList) {
            OaTodo oaTodo = new OaTodo();
            oaTodo.setSubject(subject);
            oaTodo.setTodoKey(todoKey);
            oaTodo.setTodoId(todoId);
            oaTodo.setReceiptCode(receiptCode);
            oaTodo.setUserCode(userCode);
            oaTodo.setInParam(UtilObject.isNotNull(params) ? params.toString() : null);
            oaTodo.setOutParam(UtilObject.isNotNull(returnObject) ? returnObject.toString() : null);
            oaTodoList.add(oaTodo);
        }
        oaTodoDataWrap.saveBatch(oaTodoList);
    }

    /**
     * 数据库删除OA待办
     * 当待办办结或删除时，会同步删除数据库中的待办信息
     *
     * @param todoKey            待办唯一标识
     * @param userList           目标用户列表
     * @param optType             动作类型
     */
    private void deleteOaTodo(String todoKey, List<String> userList, int optType) {
        LambdaQueryWrapper<OaTodo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OaTodo::getTodoKey, todoKey);
        // 操作类型，1为设置待办为已办，2为设置目标处理人的待办为已办
        if (optType != 1) {
            queryWrapper.in(OaTodo::getUserCode, userList);
        }
        oaTodoDataWrap.remove(queryWrapper);
    }

    /**
     * 根据 审批流程id/单据主键id 获取 待办
     *
     * @param todoId 审批流程id/单据主键id
     * @return 待办
     */
    private OaTodo getOaTodoByTodoId(String todoId) {
        LambdaQueryWrapper<OaTodo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OaTodo::getTodoId, todoId);
        return oaTodoDataWrap.getOne(queryWrapper, false);
    }

    /**
     * 发送待办到OA系统
     *
     * @param subject  标题
     * @param link     链接
     * @param todoId   审批流程id/单据主键id
     * @param userList 目标用户列表
     * @throws WmsException 当接口调用失败时抛出异常
     */
    public void sendTodo(Integer sendType, String subject, String link, String todoId, List<String> userList, String receiptCode) {
        String todoKey = todoId;
        int todoType = TODO_TYPE_APPROVAL;
        // 普通待办，手动生成待办唯一标识todoKey
        if (!(HXOaIntegerfaceService.SEND_TYPE_APPROVAL == sendType)) {
            todoKey = String.valueOf(UtilSequence.nextId());
            if (HXOaIntegerfaceService.SEND_TYPE_NOTICE == sendType) {
                todoType = TODO_TYPE_NOTICE;
            }
        }

        JsonObject params = null;
        JsonObject returnObject = null;
        if (UtilConst.getInstance().isOaSyncMode()) {
            log.info("开始发送待办到OA - 业务标识: {}, 标题: {}, 目标用户数: {}", todoId, subject, userList.size());

            params = getHeadParams(ACTION_SEND_TODO);

            // 构建link
            String reLink = redirectUrl + "?param=" + UtilAES.encrypt(todoId, aesKey);


            params.getAsJsonObject("input")
                    .add("arg0", buildSendTodoParams(subject, reLink, todoKey, userList, ACTION_SEND_TODO, receiptCode, todoType));

            returnObject = sapApiCallProxy.callHXSapApi(UtilConst.getInstance().getOaUrl(), params);
            handleOaResponse(returnObject, "发送待办");

            log.info("待办发送成功 - 业务标识: {}", todoId);
        }
        // 数据库保存OA待办
        this.saveOaTodo(subject, todoKey, todoId, receiptCode, userList, params, returnObject);
    }

    /**
     * 完成OA系统中的待办
     *
     * @param todoId   审批流程id/单据主键id
     * @param userList 目标用户列表
     * @throws WmsException 当接口调用失败时抛出异常
     */
    public void completeTodo(Integer sendType, String todoId, List<String> userList, String receiptCode) {
        String todoKey = todoId;
        // 普通待办，获取待办唯一标识todoKey
        if (!(HXOaIntegerfaceService.SEND_TYPE_APPROVAL == sendType)) {
            OaTodo oaTodo = getOaTodoByTodoId(todoId);
            if (UtilObject.isNotNull(oaTodo)) {
                todoKey = oaTodo.getTodoKey();
            }
        }

        if (UtilConst.getInstance().isOaSyncMode()) {
            log.info("开始完成OA待办 - 业务标识: {}, 目标用户数: {}", todoId, userList.size());

            JsonObject params = getHeadParams(ACTION_COMPLETE_TODO);
            params.getAsJsonObject("input")
                    .add("arg0", buildSendTodoParams("", "", todoKey, userList, ACTION_COMPLETE_TODO, receiptCode, TODO_TYPE_APPROVAL));

            JsonObject returnObject = sapApiCallProxy.callHXSapApi(UtilConst.getInstance().getOaUrl(), params);
            handleOaResponse(returnObject, "完成待办");

            log.info("待办完成成功 - 业务标识: {}", todoId);
        }
        // 数据库删除OA待办
        this.deleteOaTodo(todoKey, userList, OPT_TYPE_DELETE);
    }

    /**
     * 删除OA系统中的待办
     *
     * @param todoId   审批流程id/单据主键id
     * @param userList 目标用户列表
     * @throws WmsException 当接口调用失败时抛出异常
     */
    public void deleteTodo(Integer sendType, String todoId, List<String> userList, String receiptCode) {
        String todoKey = todoId;
        // 普通待办，获取待办唯一标识todoKey
        if (!(HXOaIntegerfaceService.SEND_TYPE_APPROVAL == sendType)) {
            OaTodo oaTodo = getOaTodoByTodoId(todoId);
            if (UtilObject.isNotNull(oaTodo)) {
                todoKey = oaTodo.getTodoKey();
            }
        }

        if (UtilConst.getInstance().isOaSyncMode()) {
            log.info("开始删除OA待办 - 业务标识: {}, 目标用户数: {}", todoId, userList.size());

            JsonObject params = getHeadParams(ACTION_DELETE_TODO);
            params.getAsJsonObject("input")
                    .add("arg0", buildSendTodoParams("", "", todoKey, userList, ACTION_DELETE_TODO, receiptCode, TODO_TYPE_APPROVAL));

            JsonObject returnObject = sapApiCallProxy.callHXSapApi(UtilConst.getInstance().getOaUrl(), params);
            handleOaResponse(returnObject, "删除待办");

            log.info("待办删除成功 - 业务标识: {}", todoId);
        }
        // 数据库删除OA待办
        this.deleteOaTodo(todoKey, userList, OPT_TYPE_DELETE);
    }

}
