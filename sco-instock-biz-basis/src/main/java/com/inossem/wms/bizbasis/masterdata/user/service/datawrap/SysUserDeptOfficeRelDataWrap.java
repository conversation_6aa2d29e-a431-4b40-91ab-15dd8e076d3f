package com.inossem.wms.bizbasis.masterdata.user.service.datawrap;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.inossem.wms.bizbasis.masterdata.user.dao.SysUserDeptOfficeRelMapper;
import com.inossem.wms.common.enums.dept.EnumDept;
import com.inossem.wms.common.enums.dept.EnumOffice;
import com.inossem.wms.common.enums.workflow.EnumApprovalLevel;
import com.inossem.wms.common.model.auth.rel.entity.SysUserDeptOfficeRel;
import com.inossem.wms.common.model.auth.user.dto.SysUserDTO;
import com.inossem.wms.common.model.auth.user.dto.UserJobDTO;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.metadata.po.MetaDataDeptOfficePO;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import com.inossem.wms.common.util.UtilCollection;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 用户科室部门关系表 服务实现类
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-02-27
 */
@Service
public class SysUserDeptOfficeRelDataWrap extends BaseDataWrap<SysUserDeptOfficeRelMapper, SysUserDeptOfficeRel> {

    public List<UserJobDTO> getUserJobLevel(Long userId) {
        QueryWrapper<SysUserDeptOfficeRel> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("DISTINCT job_level").lambda().eq(SysUserDeptOfficeRel::getUserId, userId);
        List<SysUserDeptOfficeRel> list = this.baseMapper.selectList(queryWrapper);
        List<UserJobDTO> userJobDTOList = new ArrayList<>();
        list.forEach(a -> {
            UserJobDTO userJobDTO = new UserJobDTO();
            userJobDTO.setLevelId(a.getJobLevel());
            userJobDTOList.add(userJobDTO);
        });
        return userJobDTOList;
    }

    /**
     * 获取审批用户Code
     *
     * @param dept   部门code
     * @param office 科室code
     * @param level  审批级别
     * @return String
     */
    public List<String> getApproveUserCode(EnumDept dept, EnumOffice office, EnumApprovalLevel level) {
        MetaDataDeptOfficePO po = new MetaDataDeptOfficePO();
        po.setDeptCode(dept.getCode());
        po.setDeptOfficeCode(office.getCode());
        po.setJobLevel(level.getValue());
        List<SysUserDTO> list = this.baseMapper.getApproveUserList(po);
        return list.stream().map(SysUserDTO::getUserCode).distinct().collect(Collectors.toList());
    }

    /**
     * 获取审批用户Code
     *
     * @param dept       部门code
     * @param officeCode 科室code
     * @param userName   用户名
     * @return 审批人code集合
     */
    public List<String> getApproveUserCode(EnumDept dept, String officeCode, List<String> userName) {
        MetaDataDeptOfficePO po = new MetaDataDeptOfficePO();
        po.setDeptCode(dept.getCode());
        po.setDeptOfficeCode(officeCode);
        po.setUserName(userName);
        List<SysUserDTO> list = this.baseMapper.getApproveUserListWithUserName(po);
        return list.stream().map(SysUserDTO::getUserCode).distinct().collect(Collectors.toList());
    }

    /**
     * 获取审批用户Code
     *
     * @param deptCode   部门code
     * @param officeCode 科室code
     * @param level      审批级别
     * @return String
     */
    public List<String> getApproveUserCode(String deptCode, String officeCode, EnumApprovalLevel level) {
        MetaDataDeptOfficePO po = new MetaDataDeptOfficePO();
        po.setDeptCode(deptCode);
        po.setDeptOfficeCode(officeCode);
        po.setJobLevel(level.getValue());
        List<SysUserDTO> list = this.baseMapper.getApproveUserList(po);
        return list.stream().map(SysUserDTO::getUserCode).distinct().collect(Collectors.toList());
    }


    /**
     * 获取用户所属部门
     *
     * @param currentUser 当前用户
     * @return 部门类型
     */
    public List<MetaDataDeptOfficePO> getUserDept(CurrentUser currentUser) {
        MetaDataDeptOfficePO po = new MetaDataDeptOfficePO();
        po.setUserId(currentUser.getId());
        return this.baseMapper.getUserDept(po);
    }

    public List<MetaDataDeptOfficePO> getUserDept(Long userId) {
        MetaDataDeptOfficePO po = new MetaDataDeptOfficePO();
        po.setUserId(userId);
        return this.baseMapper.getUserDept(po);
    }
}
