<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizbasis.masterdata.user.dao.SysUserDeptOfficeRelMapper">

    <!-- 增加Mybatis缓存，针对审批人查询。如果有不需要走缓存的，需要在SQL select 层配置 -->
    <cache eviction="FIFO" flushInterval="150000" size="1024" readOnly="true"
           type="org.mybatis.caches.ehcache.EhcacheCache"/>


    <select id="getApproveUserList" resultType="com.inossem.wms.common.model.auth.user.dto.SysUserDTO">
        SELECT
        SU.id,
        SU.user_code,
        SU.user_name,
        DD.id dept_id,
        DD.dept_code,
        DD.dept_name
        FROM
        sys_user_dept_office_rel SUDOR
        JOIN sys_user SU ON SUDOR.user_id = SU.id
        LEFT JOIN dic_dept DD ON DD.id = SUDOR.dept_id and DD.is_delete = 0
        <if test="deptCode != null and deptCode != '' ">
            JOIN dic_dept DD ON SUDOR.dept_id = DD.id
        </if>
        <if test="deptOfficeCode != null and deptOfficeCode != '' ">
            JOIN dic_dept_office DDO ON SUDOR.office_id = DDO.id
        </if>
        <where>
            <if test="deptCode != null and deptCode != '' ">
                AND DD.dept_code = #{deptCode}
            </if>
            <if test="deptOfficeCode != null and deptOfficeCode != ''">
                AND DDO.dept_office_code = #{deptOfficeCode}
            </if>
            <if test="jobLevel != null">
                AND SUDOR.job_level = #{jobLevel}
            </if>
        </where>
        group by SU.id
    </select>

    <select id="getApproveUserListWithUserName" resultType="com.inossem.wms.common.model.auth.user.dto.SysUserDTO">
        SELECT
        SU.id,
        SU.user_code,
        SU.user_name
        FROM
        sys_user_dept_office_rel SUDOR
        JOIN sys_user SU ON SUDOR.user_id = SU.id
        JOIN dic_dept DD ON SUDOR.dept_id = DD.id
        <if test="deptOfficeCode != null and deptOfficeCode != '' ">
            JOIN dic_dept_office DDO ON SUDOR.office_id = DDO.id
        </if>
        <where>
            <if test="deptCode != null and deptCode != '' ">
                AND DD.dept_code = #{deptCode}
            </if>
            <if test="userName != null and userName != '' ">
                AND SU.user_name in
                <foreach collection="po.userName" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getUserDept" resultType="com.inossem.wms.common.model.metadata.po.MetaDataDeptOfficePO">
        SELECT DD.id AS deptId,
               DD.dept_code,
               DD.dept_name,
               DD.dept_type,
               DDO.id AS deptOfficeId,
               DDO.dept_office_code,
               DDO.dept_office_name
        FROM sys_user_dept_office_rel SUDOR
                 JOIN dic_dept DD ON SUDOR.dept_id = DD.id
                 LEFT JOIN dic_dept_office DDO ON SUDOR.office_id = DDO.id
        WHERE SUDOR.user_id = #{userId}
        GROUP BY DD.dept_code
    </select>


</mapper>
