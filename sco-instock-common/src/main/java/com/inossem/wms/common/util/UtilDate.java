package com.inossem.wms.common.util;

import cn.hutool.core.date.DateUtil;
import com.inossem.wms.common.constant.Const;
import org.springframework.lang.Nullable;

import javax.validation.constraints.NotNull;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;

/**
 * <AUTHOR>
 */
public class UtilDate {

    /**
     * 返回非空日期字符串
     * 
     * @param dateTime 日期时间
     * @return yyyy-MM-dd
     */
    @SuppressWarnings("unused")
    public static String getStringDateTimeForDate(Date dateTime) {
        if (dateTime == null) {
            return Const.STRING_EMPTY;
        } else {
            LocalDateTime ldt = dateTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
            return ldt.format(Const.FORMATTER_DATETIME1);
        }
    }

    /**
     * 返回非空日期字符串
     * 
     * @param dateTime 日期时间
     * @return yyyy-MM-dd
     */
    public static String getStringDateForDate(Date dateTime) {
        if (dateTime == null) {
            return Const.STRING_EMPTY;
        } else {
            LocalDateTime ldt = dateTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
            return ldt.format(Const.FORMATTER_DATE1);
        }
    }

    /**
     * 返回非空日期字符串
     * @param dateTime
     * @param patten
     * @return
     */
    public static String getStringDateForDate(Date dateTime, DateTimeFormatter patten) {
        if (dateTime == null) {
            return Const.STRING_EMPTY;
        } else {
            LocalDateTime ldt = dateTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
            return ldt.format(patten);
        }
    }
    /**
     * 返回非空时间字符串
     * 
     * @param dateTime 日期时间
     * @return HH:mm:ss
     */
    @SuppressWarnings("unused")
    public static String getStringTimeForDate(Date dateTime) {
        if (dateTime == null) {
            return Const.STRING_EMPTY;
        } else {
            LocalDateTime ldt = dateTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
            return ldt.format(Const.FORMATTER_TIME);
        }
    }
    /**
     * 得到系统当前日期时间
     * 
     * @return 当前日期时间
     */
    public static Date getNow() {
        return new Date();
    }

    /**
     * 时间校验
     * 
     * @param param 需要校验时间
     * @param beginDateTime 开始时间
     * @param endDateTime 结束时间
     * @return 如果需要校验时间在起止日期之内返回true
     */
    @SuppressWarnings("unused")
    public static boolean checkDate(@NotNull Date param, @Nullable Date beginDateTime, @Nullable Date endDateTime) {
        if (UtilObject.isNull(param)) {
            return false;
        }
        if (beginDateTime == null) {
            if (endDateTime == null) {
                return true;
            } else {
                return !param.after(endDateTime);
            }
        } else {
            if (param.before(beginDateTime)) {
                return false;
            } else if (endDateTime == null) {
                return true;
            } else {
                return !param.after(endDateTime);
            }
        }
    }

    /**
     * 加n天
     * 
     * @param date 指定日期
     * @param day 天数增量
     * @return 日期结果
     */
    @SuppressWarnings("unused")
    public static Date plusDays(Date date, int day) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DATE, day);
        return cal.getTime();
    }

    /**
     * 加n月
     * 
     * @param date 指定日期
     * @param month 月份增量
     * @return 日期结果
     */
    @SuppressWarnings("unused")
    public static Date plusMonths(Date date, int month) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.MONTH, month);
        return cal.getTime();
    }

    /**
     * 加n年
     * 
     * @param date 指定日期
     * @param year 年份增量
     * @return 日期结果
     */
    @SuppressWarnings("unused")
    public static Date plusYears(Date date, int year) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.YEAR, year);
        return cal.getTime();
    }

    @SuppressWarnings("unused")
    public static double getTimeDiff(Date begin, Date end) {
        return (end.getTime() - begin.getTime()) / 1000.0;
    }

    /**
     * 返回本月第一天
     *
     * @return date
     */
    @SuppressWarnings("unused")
    public static Date getFirstDayOfMonth() {
        Calendar  cal=Calendar.getInstance();//获取当前日期
        cal.set(Calendar.DAY_OF_MONTH,1);//设置为1号,当前日期既为本月第一天
        cal.set(Calendar.HOUR_OF_DAY,0);
        cal.set(Calendar.MINUTE,0);
        cal.set(Calendar.SECOND,0);
        cal.set(Calendar.MILLISECOND,0);
        return cal.getTime();
    }

    /**
     * 获取年月yyMM
     */
    public static String getYearMonth(Date date) {
        return new SimpleDateFormat("yyMM", Locale.CHINESE).format(date);
    }


    public static Date getBeginOfDay(Date date) {
        if (UtilObject.isNull(date)) {
            return null;
        }
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(date.getTime()), ZoneId.systemDefault());
        LocalDateTime endOfDay = localDateTime.with(LocalTime.MIN);
        return Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static Date getEndOfDay(Date date) {
        if (UtilObject.isNull(date)) {
            return null;
        }
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(date.getTime()), ZoneId.systemDefault());
        LocalDateTime endOfDay = localDateTime.with(LocalTime.MAX);
        return Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());
    }

    // 获得某天最小时间 2022-12-09 00:00:00
    public static Date getStartOfDay(Date date) {
        if (UtilObject.isNull(date)) {
            return null;
        }
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(date.getTime()), ZoneId.systemDefault());
        LocalDateTime startOfDay = localDateTime.with(LocalTime.MIN);
        return Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 日期格式字符串转Date
     */
    public static Date convertToDate(String dateStr){
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date parse = null;
        try {
            parse = dateFormat.parse(dateStr);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        return parse;
    }

    /**
     * 将 date日期转为 yyyyMMdd 格式字符串
     *
     * @param dateTime 日期时间
     * @return yyyy-MM-dd
     */
    public static String convertDateToDateStr(Date dateTime) {
        if (dateTime == null) {
            return Const.STRING_EMPTY;
        } else {
            LocalDateTime ldt = dateTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
            return ldt.format(Const.FORMATTER_DATE2);
        }
    }

    /**
     * <AUTHOR>
     *
     * 计算两个日期之间的相差天数
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 相差天数
     */
    public static Integer getDaysDifference(Date startDate, Date endDate){
        if (startDate == null || endDate == null) {
            return Const.ZERO;
        }

        // 计算相差毫秒数
        long diffMillis = UtilDate.getStartOfDay(endDate).getTime() - UtilDate.getStartOfDay(startDate).getTime();

        // 转换为天数差（24小时为一天）
        int daysDifference = (int) (diffMillis / (1000 * 60 * 60 * 24));

        return daysDifference;
    }

    /**
     * 获取两个日期中的最大日期
     *
     * @param date1 日期1
     * @param date2 日期2
     * @return 最大日期
     */
    public static Date maxDate(Date date1, Date date2) {
        if (UtilObject.isNull(date1)) {
            return date2;
        }
        if (UtilObject.isNull(date2)) {
            return date1;
        }
        if (date1.after(date2)) {
            return date1;
        }
        return date2;
    }

    /**
     * 计算两个日期之间去掉周六周日的天数（工作日天数）
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 工作日天数（排除周六周日）
     */
    public static int getWorkDays(Date startDate, Date endDate) {
        if (startDate == null || endDate == null) {
            return Const.ZERO;
        }

        Calendar cal = Calendar.getInstance();
        cal.setTime(startDate);

        int workDays = 0;
        while (!cal.getTime().after(endDate)) {
            int dayOfWeek = cal.get(Calendar.DAY_OF_WEEK);
            // 排除周六(7)和周日(1)
            if (dayOfWeek != Calendar.SATURDAY && dayOfWeek != Calendar.SUNDAY) {
                workDays++;
            }
            cal.add(Calendar.DAY_OF_MONTH, 1); // 增加1天
        }
        return workDays;
    }

    /**
     * 判断两个日期之间是否是整月
     *
     * @param startDate 2022-01-01
     * @param endDate   2022-02-31
     * @return true: 整月 (整多个月也是整月)
     */
    public static boolean isFullMonth(Date startDate, Date endDate) {
        if (UtilObject.isNull(startDate) || UtilObject.isNull(endDate)) {
            return false;
        }

        if (DateUtil.dayOfMonth(startDate) == 1 && DateUtil.isLastDayOfMonth(endDate)) {
            return true;
        }
        return false;
    }

}
