package com.inossem.wms.system.workflow.listener;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.sap.restful.service.HXOaIntegerfaceService;
import com.inossem.wms.bizbasis.todo.service.biz.StWftaskService;
import com.inossem.wms.common.annotation.WmsMQListener;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.workflow.EnumApprovalNode;
import com.inossem.wms.common.enums.workflow.EnumApprovalStatus;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.approval.dto.ActivitiCommentDTO;
import com.inossem.wms.common.model.approval.dto.BizApprovalReceiptInstanceRelDTO;
import com.inossem.wms.common.model.approval.dto.CommunicateTaskItemDTO;
import com.inossem.wms.common.model.approval.dto.ProcessInstanceDTO;
import com.inossem.wms.common.model.approval.dto.RevokeDTO;
import com.inossem.wms.common.model.approval.entity.BizApprovalReceiptInstanceRel;
import com.inossem.wms.common.model.approval.po.CommunicateTaskPO;
import com.inossem.wms.common.model.approval.po.StartProcessInstancePO;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilCurrentContext;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.UtilString;
import com.inossem.wms.system.proxy.service.datawrap.ProxyUserDataWrap;
import com.inossem.wms.system.workflow.service.business.biz.CumtomJumpFlowNodeCommand;
import com.inossem.wms.system.workflow.service.business.biz.WorkflowService;
import com.inossem.wms.system.workflow.service.business.datawrap.BizApprovalReceiptInstanceRelDataWrap;
import com.inossem.wms.system.workflow.service.process.biz.ProcessDefinitionService;
import lombok.extern.slf4j.Slf4j;
import org.activiti.bpmn.model.BpmnModel;
import org.activiti.engine.HistoryService;
import org.activiti.engine.RepositoryService;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.TaskService;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.history.HistoricProcessInstance;
import org.activiti.engine.impl.TaskServiceImpl;
import org.activiti.engine.impl.persistence.entity.TaskEntity;
import org.activiti.engine.task.IdentityLink;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 审批发起监听接口
 *
 * <AUTHOR>
 * @date 2020/9/2 10:42
 */
@Slf4j
@Service
public class ApprovalListener {

    @Autowired
    protected BizApprovalReceiptInstanceRelDataWrap bizApprovalReceiptInstanceRelDataWrap;

    @Autowired
    protected DataFillService dataFillService;

    @Autowired
    private WorkflowService workflowService;

    @Autowired
    private HistoryService historyServiceImpl;
    @Autowired
    protected BizCommonService bizCommonService;
    @Autowired
    protected ProcessDefinitionService processDefinitionService;
    @Autowired
    private HXOaIntegerfaceService hXOaIntegerfaceService;
    @Autowired
    private ProxyUserDataWrap proxyUserDataWrap;
    @Autowired
    protected StWftaskService stWftaskService;
    @Autowired
    private TaskService taskServiceImpl;

    @Autowired
    private RepositoryService repositoryServiceImpl;

    @Autowired
    private RuntimeService runtimeServiceImpl;

    /**
     * 发起审批
     *
     * @param startProcessInstancePo 发起审批入参
     */
    @WmsMQListener(tags = Const.START_APPROVAL_REQUEST_CODE)
    public void receiveStartApproval(StartProcessInstancePO startProcessInstancePo) {
        ProcessInstanceDTO processInstanceDto = workflowService.startProcessInstance(startProcessInstancePo);

        // 保存业务与审批关系
        BizApprovalReceiptInstanceRel approvalInfo = UtilBean.newInstance(startProcessInstancePo, BizApprovalReceiptInstanceRel.class);
        approvalInfo.setCreateUserId(startProcessInstancePo.getUserId());
        approvalInfo.setApproveStatus(EnumApprovalStatus.APPROVING.getValue());
        approvalInfo.setProcessInstanceId(processInstanceDto.getProcessInstanceId());
        approvalInfo.setReceiptHeadId(startProcessInstancePo.getReceiptId());
        bizApprovalReceiptInstanceRelDataWrap.save(approvalInfo);
    }

    public void receiveRevokeApproval(StartProcessInstancePO startProcessInstancePo) {
        QueryWrapper<BizApprovalReceiptInstanceRel> query = new QueryWrapper<>();
        query.lambda().eq(BizApprovalReceiptInstanceRel::getReceiptCode, startProcessInstancePo.getReceiptCode())
                .eq(BizApprovalReceiptInstanceRel::getReceiptType, startProcessInstancePo.getReceiptType());
        List<BizApprovalReceiptInstanceRel> approvalList = bizApprovalReceiptInstanceRelDataWrap.list(query);
        List<BizApprovalReceiptInstanceRelDTO> taskList = UtilCollection.toList(approvalList, BizApprovalReceiptInstanceRelDTO.class);

        dataFillService.fillRlatAttrDataList(taskList);

        if (!CollectionUtils.isEmpty(taskList)) {
            for (BizApprovalReceiptInstanceRelDTO taskCo : taskList) {
                if (0 == taskCo.getApproveStatus()) {
                    RevokeDTO revokeRequest = new RevokeDTO();
                    revokeRequest.setProcessInstanceId(taskCo.getProcessInstanceId());
                    revokeRequest.setDeleteReason("单据删除");
                    workflowService.revoke(revokeRequest);
                }
                // 删除历史记录
                historyServiceImpl.deleteHistoricProcessInstance(taskCo.getProcessInstanceId());
            }
        }
    }

    /**
     * 设置审批人
     *
     * @param task         DelegateTask
     * @param userCodeList userCodeList
     */
    protected void addApproveUser(DelegateTask task, List<String> userCodeList) {

        if (UtilCollection.isEmpty(userCodeList)) {
            // 待设置的用户列表为空时，直接返回
            return;
        }
//        Long ftyId = (Long) task.getVariable("ftyId");
//        String ftyCode = (String) task.getVariable("ftyCode");
//        String taskType=stWftaskService.getTaskType(ftyId,ftyCode);
        // 先对userCode列表去重
        List<String> userCodes = userCodeList.stream().distinct().collect(Collectors.toList());

        String subject = (String) task.getVariable("subject");
        if(UtilString.isNotNullOrEmpty(subject)){
            // 发送待办到OA系统
            hXOaIntegerfaceService.sendTodo(HXOaIntegerfaceService.SEND_TYPE_APPROVAL, subject, "", task.getId(), userCodes, UtilObject.getStringOrEmpty(task.getVariable(Const.RECEIPT_CODE)));
        }
        // 待审批
        if (UtilCollection.isNotEmpty(userCodes)) {
            task.addCandidateUsers(userCodes);
            Set<IdentityLink> candidates = task.getCandidates();
            candidates.forEach(candidate -> {
                log.debug("单据：{},下级审批候选人type：{}, 编号：{}", task.getVariable(Const.RECEIPT_CODE), candidate.getType(), candidate.getUserId());
            });
        }
    }

    /**
     * 审批结束回调
     *
     * @param delegateExecution delegateExecution
     * @param mqKey             mqKey
     */
    protected void approvalCallback(DelegateExecution delegateExecution, String mqKey) {
        String currentId = delegateExecution.getCurrentActivityId();
        if (EnumApprovalNode.END_NODE.getValue().equals(currentId)) {
            // 结束节点
            Map<String, Object> variables = delegateExecution.getVariables();
            if (variables.containsKey(Const.AGREE)) {
                if ((boolean) variables.get(Const.AGREE)) {
                    // 质检会签审批通过，调用业务功能
                    workflowService.approvalCallback(delegateExecution.getProcessInstanceId(), variables, EnumApprovalStatus.FINISH, mqKey);
                } else {
                    // 质检会签审批拒绝，调用业务功能
                    workflowService.approvalCallback(delegateExecution.getProcessInstanceId(), variables, EnumApprovalStatus.REJECT, mqKey);
                }
            }
        }
    }



    /**
     * 单人抄送 / 子任务
     * @param parentTask:父级任务
     * @param userCode:抄送人
     * @param taskDefinitionKey:节点顺序
     * @param taskName:节点描述
     */
    public void addSendCopy(DelegateTask parentTask, String userCode,String taskDefinitionKey,String taskName){
        List<String> userCodeList = Collections.singletonList(userCode);
        this.addSendBatchCopy(parentTask,userCodeList,taskDefinitionKey,taskName);
    }

    /**
     * 多人抄送 / 子任务
     * @param parentTask:父级任务
     * @param userCodeList:抄送人
     * @param taskDefinitionKey:节点顺序
     * @param taskName:节点描述
     */
    public void addSendBatchCopy(DelegateTask parentTask, List<String> userCodeList,String taskDefinitionKey,String taskName){
        for (String userCode:userCodeList) {
            TaskEntity subTask = (TaskEntity) workflowService.newTask();
            subTask.setAssignee(userCode);
            subTask.setName(taskName);
            subTask.setParentTaskId(parentTask.getId());//父任务id
            subTask.setProcessInstanceId(parentTask.getProcessInstanceId());
            subTask.setProcessDefinitionId(parentTask.getProcessDefinitionId());
            subTask.setExecutionId(parentTask.getExecutionId());
            subTask.setTaskDefinitionKey(taskDefinitionKey);
            workflowService.saveTask(subTask);
        }
    }

    /**
     * 跳转审批节点
     * @param delegateTask:任务
     * @return:是否要进行跳转 true:进行跳转 false:不进行跳转
     */
    public boolean jumpApprovalNode(DelegateTask delegateTask){
        Map<String, Object> variables = taskServiceImpl.getVariables(delegateTask.getId());

        // 单据id
        Long receiptId = (Long)variables.get(Const.RECEIPT_ID);

        // 获取单据的自定义跳转节点
        BizApprovalReceiptInstanceRel bizApprovalReceiptInstanceRel = bizApprovalReceiptInstanceRelDataWrap.getOne(new LambdaQueryWrapper<BizApprovalReceiptInstanceRel>()
                        .eq(BizApprovalReceiptInstanceRel::getReceiptHeadId, receiptId)
                        .isNotNull(BizApprovalReceiptInstanceRel::getJumpApprovalNode)
                        .ne(BizApprovalReceiptInstanceRel::getJumpApprovalNode,  Const.STRING_EMPTY)
                , false);

        // 如果当前流程中没有设置跳转节点，则直接返回
        if(bizApprovalReceiptInstanceRel == null || StringUtils.isEmpty(bizApprovalReceiptInstanceRel.getJumpApprovalNode())){
            return false;
        }

        // 如果当前节点和跳转节点一致，则直接返回
        if(delegateTask.getTaskDefinitionKey().equals(bizApprovalReceiptInstanceRel.getJumpApprovalNode())){
            return false;
        }

        return true;
    }

    /**
     * <AUTHOR>
     *
     * 判断是否是沟通任务
     * 如果是沟通任务，则把任务信息存入Comment中
     *
     * @param delegateTask
     * @return:是否是沟通任务 true:是沟通任务 false:不是沟通任务
     */
    public boolean isCommunicateTask(DelegateTask delegateTask) {
        if(!EnumApprovalNode.COMMUNICATE_NODE.getValue().equals(delegateTask.getTaskDefinitionKey())){
            return false;
        }

        String communicateTaskInfoStr  = (String) delegateTask.getVariable("activitiCommentInfo");

        // 沟通任务中必须要有沟通任务信息
        if(communicateTaskInfoStr == null){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }

        ActivitiCommentDTO communicateTaskInfo = JSONObject.parseObject(communicateTaskInfoStr, ActivitiCommentDTO.class);

        // 把流程变量中的沟通任务信息存入到task的Comment中
        taskServiceImpl.addComment(delegateTask.getId(), delegateTask.getProcessInstanceId(), communicateTaskInfoStr);

        // 获取沟通任务中需要审批的候选用户
        List<String> userCodes = communicateTaskInfo.getCommunicateTaskHeadDTO().getItemList().stream().map(CommunicateTaskItemDTO::getUserCode).distinct().collect(Collectors.toList());

        String subject = (String) delegateTask.getVariable("subject");
        if(UtilString.isNotNullOrEmpty(subject)){

            subject = subject.replaceAll("请审批", StringUtils.join("请查看", communicateTaskInfo.getCommunicateTaskHeadDTO().getUserName(), "的沟通并处理流程"));
            // 发送待办到OA系统
            hXOaIntegerfaceService.sendTodo(HXOaIntegerfaceService.SEND_TYPE_APPROVAL, subject, "", delegateTask.getProcessInstanceId(), userCodes, UtilObject.getStringOrEmpty(delegateTask.getVariable(Const.RECEIPT_CODE)));
        }

        // 发起沟通的用户也需要添加审批权限
        userCodes.add(communicateTaskInfo.getCommunicateTaskHeadDTO().getUserCode());

        // 待审批
        if (UtilCollection.isNotEmpty(userCodes)) {
            delegateTask.addCandidateUsers(userCodes);
        }

        log.info("创建沟通任务完毕,任务ID {}", delegateTask.getId());

        return true;
    }

    /**
     * <AUTHOR>
     *
     * 判断是否是转办任务
     * 如果是转办任务，则把任务信息存入Comment中，并直接完结该任务留下日志
     *
     * @param delegateTask
     * @return:是否是沟通任务 true:是沟通任务 false:不是沟通任务
     */
    public boolean isTransferTask(DelegateTask delegateTask) {
        if(!EnumApprovalNode.TRANSFER_NODE.getValue().equals(delegateTask.getTaskDefinitionKey())){
            return false;
        }

        String communicateTaskInfoStr  = (String) delegateTask.getVariable("activitiCommentInfo");

        // 把流程变量中的沟通任务信息存入到task的Comment中
        taskServiceImpl.addComment(delegateTask.getId(), delegateTask.getProcessInstanceId(), communicateTaskInfoStr);

        // 完结该任务留下日志
        taskServiceImpl.claim(delegateTask.getId(), UtilCurrentContext.getCurrentUser().getUserCode());
        taskServiceImpl.complete(delegateTask.getId());

        log.info("创建转办任务完毕,任务ID {}", delegateTask.getId());

        return true;
    }
}
