package com.inossem.wms.system.workflow.listener.contract;

import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDeptOfficeRelDataWrap;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.workflow.EnumApprovalLevel;
import com.inossem.wms.common.enums.workflow.EnumApprovalNode;
import com.inossem.wms.common.model.metadata.po.MetaDataDeptOfficePO;
import com.inossem.wms.system.workflow.listener.ApprovalListener;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.ExecutionListener;
import org.activiti.engine.delegate.TaskListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 服务工程确认单 审批流程监听
 *
 * <AUTHOR>
 * @since 2025-07-17
 */
@Service
public class ListenerContractConfirm extends ApprovalListener implements TaskListener, ExecutionListener {

    private static final long serialVersionUID = 1L;

    @Autowired
    private SysUserDeptOfficeRelDataWrap sysUserDeptOfficeRelDataWrap;

    @Override
    public void notify(DelegateExecution execution) {
        approvalCallback(execution, TagConst.APPROVAL_CONTRACT_CONFIRM);
    }

    @Override
    public void notify(DelegateTask delegateTask) {

        // 如果是沟通任务，则直接返回
        if (this.isCommunicateTask(delegateTask)) {
            return;
        }

        // 如果是转办任务，则直接返回
        if (this.isTransferTask(delegateTask)) {
            return;
        }

        // 如果跳转审批节点，则直接返回
        if (this.jumpApprovalNode(delegateTask)) {
            return;
        }

        String taskDefKey = delegateTask.getTaskDefinitionKey();
        String level1ApproveUserCode = (String) delegateTask.getVariable("level1ApproveUserCode");
        List<MetaDataDeptOfficePO> userDeptList = (List<MetaDataDeptOfficePO>) delegateTask.getVariable("userDept");

        // 根据节点配置审批人
        if (EnumApprovalNode.LEVEL_1_APPROVAL_NODE.getValue().equals(taskDefKey)) {
            // 一级审批节点 - 专工
            addApproveUser(delegateTask, Collections.singletonList(level1ApproveUserCode));

        } else if (EnumApprovalNode.LEVEL_2_APPROVAL_NODE.getValue().equals(taskDefKey)) {
            // 二级审批节点 - 专工所属部门领导
            List<String> level3UserList = new ArrayList<>();
            for (MetaDataDeptOfficePO deptOffice : userDeptList) {
                level3UserList.addAll(sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptOffice.getDeptCode(), null, EnumApprovalLevel.LEVEL_2));
            }
            addApproveUser(delegateTask, level3UserList);
        }
    }

}
