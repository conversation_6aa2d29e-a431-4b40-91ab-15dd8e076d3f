package com.inossem.wms.bizdomain.contract.service.component;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptAttachmentService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptOperationLogService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptRelationService;
import com.inossem.wms.bizbasis.masterdata.user.dao.SysUserDeptOfficeRelMapper;
import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDataWrap;
import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDeptOfficeRelDataWrap;
import com.inossem.wms.bizbasis.sap.restful.service.HXOaIntegerfaceService;
import com.inossem.wms.bizdomain.contract.service.datawrap.BizReceiptContractAttendanceItemDataWrap;
import com.inossem.wms.bizdomain.contract.service.datawrap.BizReceiptContractExamineItemDataWrap;
import com.inossem.wms.bizdomain.contract.service.datawrap.BizReceiptContractReceivingHeadDataWrap;
import com.inossem.wms.bizdomain.contract.service.datawrap.BizReceiptContractReceivingItemDataWrap;
import com.inossem.wms.bizdomain.contract.service.datawrap.BizReceiptContractSubItemDataWrap;
import com.inossem.wms.bizdomain.contract.service.datawrap.BizReceiptContractDieselRecheckItemDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.enums.EnumReceiptOperationType;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.EnumSequenceCode;
import com.inossem.wms.common.enums.apply.EnumReceiveType;
import com.inossem.wms.common.enums.workflow.EnumApprovalLevel;
import com.inossem.wms.common.enums.workflow.EnumApprovalStatus;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.approval.dto.BizApprovalReceiptInstanceRelDTO;
import com.inossem.wms.common.model.approval.dto.BizApproveRecordDTO;
import com.inossem.wms.common.model.approval.dto.RevokeDTO;
import com.inossem.wms.common.model.auth.user.dto.SysUserDTO;
import com.inossem.wms.common.model.auth.user.entity.SysUser;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptRelation;
import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptApplyHeadDTO;
import com.inossem.wms.common.model.bizdomain.contract.dto.BizReceiptContractAttendanceItemDTO;
import com.inossem.wms.common.model.bizdomain.contract.dto.BizReceiptContractExamineItemDTO;
import com.inossem.wms.common.model.bizdomain.contract.dto.BizReceiptContractReceivingHeadDTO;
import com.inossem.wms.common.model.bizdomain.contract.dto.BizReceiptContractReceivingItemDTO;
import com.inossem.wms.common.model.bizdomain.contract.dto.BizReceiptContractSubItemDTO;
import com.inossem.wms.common.model.bizdomain.contract.entity.BizReceiptContractAttendanceItem;
import com.inossem.wms.common.model.bizdomain.contract.entity.BizReceiptContractExamineItem;
import com.inossem.wms.common.model.bizdomain.contract.entity.BizReceiptContractReceivingHead;
import com.inossem.wms.common.model.bizdomain.contract.entity.BizReceiptContractReceivingItem;
import com.inossem.wms.common.model.bizdomain.contract.entity.BizReceiptContractSubItem;
import com.inossem.wms.common.model.bizdomain.contract.entity.BizReceiptContractDieselRecheckItem;
import com.inossem.wms.common.model.bizdomain.contract.po.BizReceiptContractReceivingSearchPO;
import com.inossem.wms.common.model.bizdomain.contract.vo.BizReceiptContractReceivingPageVO;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.ButtonVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.metadata.po.MetaDataDeptOfficePO;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilConst;
import com.inossem.wms.common.util.UtilCurrentContext;
import com.inossem.wms.common.util.UtilDate;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.UtilString;
import com.inossem.wms.system.workflow.service.business.biz.ApprovalService;
import com.inossem.wms.system.workflow.service.business.biz.WorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import com.inossem.wms.common.model.bizdomain.contract.dto.BizReceiptContractDieselRecheckItemDTO;

/**
 * 服务工程确认单 组件层
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-09
 */
@Service
@Slf4j
public class ContractConfirmComponent {

    @Autowired
    private DataFillService dataFillService;
    @Autowired
    private BizCommonService bizCommonService;
    @Autowired
    private ReceiptRelationService receiptRelationService;
    @Autowired
    private ReceiptOperationLogService receiptOperationLogService;
    @Autowired
    private ReceiptAttachmentService receiptAttachmentService;
    @Autowired
    private ApprovalService approvalService;
    @Autowired
    private DictionaryService dictionaryService;
    @Autowired
    private WorkflowService workflowService;
    @Autowired
    private HXOaIntegerfaceService hXOaIntegerfaceService;
    @Autowired
    private BizReceiptContractReceivingHeadDataWrap bizReceiptContractReceivingHeadDataWrap;
    @Autowired
    private BizReceiptContractReceivingItemDataWrap bizReceiptContractReceivingItemDataWrap;
    @Autowired
    private BizReceiptContractSubItemDataWrap bizReceiptContractSubItemDataWrap;
    @Autowired
    private BizReceiptContractAttendanceItemDataWrap bizReceiptContractAttendanceItemDataWrap;
    @Autowired
    private BizReceiptContractExamineItemDataWrap bizReceiptContractExamineItemDataWrap;
    @Autowired
    private BizReceiptContractDieselRecheckItemDataWrap bizReceiptContractDieselRecheckItemDataWrap;
    @Autowired
    private ContractReceivingComponent contractReceivingComponent;
    @Autowired
    private SysUserDataWrap sysUserDataWrap;
    @Autowired
    private SysUserDeptOfficeRelDataWrap sysUserDeptOfficeRelDataWrap;
    @Autowired
    private SysUserDeptOfficeRelMapper sysUserDeptOfficeRelMapper;

    /**
     * 页面初始化:
     */
    public void setInit(BizContext ctx) {
        // 页面初始化设置
        BizResultVO<BizReceiptContractReceivingHeadDTO> resultVO = new BizResultVO<>(
                new BizReceiptContractReceivingHeadDTO()
                        .setReceiptType(EnumReceiptType.CONTRACT_CONFIRM.getValue())
                        .setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue())
                        .setCreateTime(UtilDate.getNow())
                        .setAssignUserList(this.getAssignUserList())
                        .setCreateUserName(ctx.getCurrentUser().getUserName()),
                new ExtendVO(), new ButtonVO().setButtonSave(true).setButtonSubmit(true));
        // 设置页面初始化数据到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 查询专工审批人
     *
     * @return 专工审批人列表
     */
    private List<SysUserDTO> getAssignUserList() {
        MetaDataDeptOfficePO po = new MetaDataDeptOfficePO();
        po.setJobLevel(EnumApprovalLevel.LEVEL_1.getValue());
        return sysUserDeptOfficeRelMapper.getApproveUserList(po);
    }

    /**
     * 开启附件
     *
     * @in ctx 入参 {@link BizResultVO ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO ("extend":"扩展功能开启附件")}
     */
    public void setExtendAttachment(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptContractReceivingHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 设置附件开启/关闭
        resultVO.getExtend().setAttachmentRequired(UtilConst.getInstance().isAttachmentRequired());
    }

    /**
     * 开启操作日志
     *
     * @in ctx 入参 {@link BizResultVO ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO ("extend":"扩展功能开启操作日志")}
     */
    public void setExtendOperationLog(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptContractReceivingHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 设置操作日志开启/关闭
        resultVO.getExtend().setOperationLogRequired(UtilConst.getInstance().isOperationLogRequired());
    }

    /**
     * 设置单据流
     *
     * @param ctx 上下文
     */
    public void setExtendRelation(BizContext ctx) {
        BizResultVO<BizReceiptContractReceivingHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        resultVO.getExtend().setRelationRequired(true);
        if (UtilObject.isNotNull(resultVO.getHead())) {
            resultVO.getHead().setRelationList(receiptRelationService.getReceiptTree(resultVO.getHead().getReceiptType(), resultVO.getHead().getId(), null));
        }
        // 设置单据流详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 开启审批
     *
     * @in ctx 入参 {@link BizResultVO (head":"采购验收","extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO (head":"采购验收及单审批信息","extend":"扩展功能开启审批")}
     */
    public void setExtendWf(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptContractReceivingHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);

        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }

        boolean wfByReceiptType = EnumReceiptType.CONTRACT_CONFIRM.getValue().equals(resultVO.getHead().getReceiptType());

        if (UtilObject.isNull(resultVO.getHead())) {
            // 初始化 - 设置审批开启/关闭
            resultVO.getExtend().setWfRequired(wfByReceiptType);
        } else {
            // 详情页 - 设置审批开启/关闭
            if (wfByReceiptType) {
                resultVO.getExtend().setWfRequired(wfByReceiptType);
                if (UtilObject.isNotNull(resultVO.getHead())) {
                    List<BizApproveRecordDTO> approveList = approvalService.getHistoryActivity(resultVO.getHead().getReceiptCode());
                    resultVO.getHead().setApproveList(approveList);
                    if (UtilCollection.isNotEmpty(approveList)) {
                        resultVO.getHead().setProInstanceId(Long.valueOf(approveList.get(approveList.size() - 1).getProInstanceId()));
                    }
                }
            }
        }
        // 设置审批详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 获取详情
     *
     * @param ctx 上下文
     */
    public void getPage(BizContext ctx) {
        // 上下文入参
        BizReceiptContractReceivingSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser user = ctx.getCurrentUser();
        // 组装查询条件
        WmsQueryWrapper<BizReceiptContractReceivingSearchPO> wrapper = this.setQueryWrapper(po, user);
        // 分页处理
        IPage<BizReceiptContractReceivingPageVO> page = po.getPageObj(BizReceiptContractReceivingPageVO.class);

        bizReceiptContractReceivingHeadDataWrap.selectPage(page, wrapper);
        List<BizReceiptContractReceivingPageVO> dtoList = page.getRecords();

        // 填充关联属性
        dataFillService.fillAttr(dtoList);

        // 设置分页信息到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(dtoList, page.getTotal()));
    }

    /**
     * 查询条件设置
     *
     * @param po   查询条件
     * @param user 当前用户
     * @return 查询条件
     */
    private WmsQueryWrapper<BizReceiptContractReceivingSearchPO> setQueryWrapper(BizReceiptContractReceivingSearchPO po, CurrentUser user) {
        if (null == po) {
            po = new BizReceiptContractReceivingSearchPO();
        }
        // 查询条件设置
        WmsQueryWrapper<BizReceiptContractReceivingSearchPO> wrapper = new WmsQueryWrapper<>();
        wrapper.likeLeft(UtilString.isNotNullOrEmpty(po.getReceiptCode()), "biz_receipt_contract_receiving_head.receipt_code", po.getReceiptCode())
                .eq(UtilNumber.isNotEmpty(po.getReceiptType()), "biz_receipt_contract_receiving_head.receipt_type", po.getReceiptType())
                .in(UtilCollection.isNotEmpty(po.getReceiptStatusList()), "biz_receipt_contract_receiving_head.receipt_status", po.getReceiptStatusList())
                .like(UtilString.isNotNullOrEmpty(po.getDesc()), "biz_receipt_contract_receiving_head.description", po.getDesc())
                .like(UtilString.isNotNullOrEmpty(po.getCreateUserName()), "sys_user.user_name", po.getCreateUserName());
        return wrapper;
    }

    /**
     * 获取单据详情
     *
     * @param ctx 入参上下文
     */
    public void getInfo(BizContext ctx) {
        // 入参上下文
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取详情
        BizReceiptContractReceivingHead head = bizReceiptContractReceivingHeadDataWrap.getById(headId);
        // 转DTO
        BizReceiptContractReceivingHeadDTO headDTO = UtilBean.newInstance(head, BizReceiptContractReceivingHeadDTO.class);
        // 填充关联属性和父子属性
        dataFillService.fillAttr(headDTO);
        // 设置按钮组权限
        ButtonVO buttonVO = this.setButton(headDTO);
        if (UtilCollection.isNotEmpty(headDTO.getSubItemList())) {
            headDTO.getSubItemList().forEach(c -> c.setCanDeliveryQty(c.getQty().subtract(c.getTotalQty())));
        }
        // 设置专工审批人
        headDTO.setAssignUserList(this.getAssignUserList());
        // 设置供应商用户
        headDTO.setSupplierId();
        // 设置审批按钮权限
        workflowService.setApproveButton(buttonVO, ctx.getContextData("taskId"));
        // 设置申请单详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(headDTO, new ExtendVO(), buttonVO));
    }

    /**
     * 按钮组
     *
     * @param headDTO 服务工程确认单
     * @return 按钮组对象
     */
    private ButtonVO setButton(BizReceiptContractReceivingHeadDTO headDTO) {
        // 单据抬头状态
        Integer receiptStatus = headDTO.getReceiptStatus();
        if (UtilObject.isNull(receiptStatus)) {
            return new ButtonVO();
        }
        ButtonVO buttonVO = new ButtonVO();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 草稿 -【保存、提交、删除】
            return buttonVO.setButtonSave(true).setButtonSubmit(true).setButtonDelete(true);
        }
        if (EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue().equals(receiptStatus)) {
            // 已驳回 -【提交】
            return buttonVO.setButtonSubmit(true);
        }
        if (EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue().equals(receiptStatus)) {
            // 审批中 -【撤销】
            return buttonVO.setButtonRevoke(true);
        }
        if (EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(receiptStatus)) {
            // 已完成 -【打印】
            return buttonVO.setButtonPrint(true);
        }
        return buttonVO;
    }

    /**
     * 保存-校验入参
     */
    public void checkSaveData(BizContext ctx) {
        // 入参上下文
        BizReceiptContractReceivingHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 抬头数据是否为空
        if (po == null) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 行项目数据是否为空
        if (UtilCollection.isEmpty(po.getItemList())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
        po.getItemList().forEach(c -> c.setQty(c.getQty().setScale(3, RoundingMode.HALF_UP)));
    }

    /**
     * 保存单据
     *
     * @param ctx ctx
     */
    public void save(BizContext ctx) {
        // 入参上下文
        BizReceiptContractReceivingHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型(为空则是保存按钮操作)
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 当前用户信息
        CurrentUser user = ctx.getCurrentUser();
        /* ********************** head处理开始 *************************/
        String code = po.getReceiptCode();
        if (UtilNumber.isEmpty(po.getId())) {
            po.setCreateUserId(user.getId());
        }
        po.setModifyUserId(user.getId());
        po.setReceiptType(EnumReceiptType.CONTRACT_CONFIRM.getValue());
        po.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
        // 验证是否为新增
        if (UtilNumber.isNotEmpty(po.getId())) {
            // 更新服务工程确认单
            bizReceiptContractReceivingHeadDataWrap.updateDtoById(po);
            // 修改前删除item、subItem
            this.deleteItem(po);

            if (null == operationLogType) {
                // 设置上下文单据日志 - 修改
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SAVE);
            }
        } else {
            code = bizCommonService.getNextSequenceValueDaily(EnumSequenceCode.SEQUENCE_CONTRACT_CONFIRM.getValue());
            po.setReceiptCode(code);
            po.setId(null);
            bizReceiptContractReceivingHeadDataWrap.saveDto(po);
            if (null == operationLogType) {
                // 设置上下文单据日志 - 创建
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_ESTABLISH);
            }
        }
        log.debug("保存服务工程确认head成功!单号{},主键{},操作人{}", code, po.getId(), user.getUserName());
        /* ********************** head处理结束 *************************/
        /* ********************** item处理开始 *************************/
        AtomicInteger rid = new AtomicInteger(1);
        for (BizReceiptContractReceivingItemDTO itemDto : po.getItemList()) {
            itemDto.setId(null);
            itemDto.setHeadId(po.getId());
            itemDto.setRid(Integer.toString(rid.getAndIncrement()));
            itemDto.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
            itemDto.setCreateUserId(user.getId());
        }
        bizReceiptContractReceivingItemDataWrap.saveBatchDto(po.getItemList());
        // 保存分项信息
        for (BizReceiptContractSubItemDTO subItem : po.getSubItemList()) {
            subItem.setId(null);
            subItem.setReceiveId(po.getId());
            subItem.setCreateUserId(user.getId());
        }
        bizReceiptContractSubItemDataWrap.saveBatchDto(po.getSubItemList());
        // 保存考勤信息
        AtomicInteger attendanceRid = new AtomicInteger(1);
        for (BizReceiptContractAttendanceItemDTO attendanceItemDTO : po.getAttendanceItemList()) {
            attendanceItemDTO.setId(null);
            attendanceItemDTO.setHeadId(po.getId());
            attendanceItemDTO.setRid(Integer.toString(attendanceRid.getAndIncrement()));
            attendanceItemDTO.setCreateUserId(user.getId());
        }
        bizReceiptContractAttendanceItemDataWrap.saveBatchDto(po.getAttendanceItemList());

        // 保存考核信息
        AtomicInteger examineRid = new AtomicInteger(1);
        for (BizReceiptContractExamineItemDTO examineItemDTO : po.getExamineItemList()) {
            examineItemDTO.setId(null);
            examineItemDTO.setHeadId(po.getId());
            examineItemDTO.setRid(Integer.toString(examineRid.getAndIncrement()));
            examineItemDTO.setCreateUserId(user.getId());
        }
        bizReceiptContractExamineItemDataWrap.saveBatchDto(po.getExamineItemList());

        // 保存柴油复核信息
        AtomicInteger dieselRecheckRid = new AtomicInteger(1);
        for (BizReceiptContractDieselRecheckItemDTO dieselRecheckItemDTO : po.getDieselRecheckItemList()) {
            dieselRecheckItemDTO.setId(null);
            dieselRecheckItemDTO.setHeadId(po.getId());
            dieselRecheckItemDTO.setRid(Integer.toString(dieselRecheckRid.getAndIncrement()));
            dieselRecheckItemDTO.setCreateUserId(user.getId());
        }
        bizReceiptContractDieselRecheckItemDataWrap.saveBatchDto(po.getDieselRecheckItemList());

        log.debug("批量保存服务工程确认item成功,code{},headId{},操作人{}", code, po.getId(), user.getUserName());
        /* ********************** item处理结束 *************************/
        // 保存单据流
        this.saveReceiptTree(ctx);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, code);
    }

    /**
     * 删除服务工程确认行项目
     *
     * @param headDTO 服务工程确认
     */
    private void deleteItem(BizReceiptContractReceivingHeadDTO headDTO) {
        UpdateWrapper<BizReceiptContractReceivingItem> wrapper = new UpdateWrapper<>();
        wrapper.lambda().eq(BizReceiptContractReceivingItem::getHeadId, headDTO.getId());
        bizReceiptContractReceivingItemDataWrap.physicalDelete(wrapper);

        UpdateWrapper<BizReceiptContractSubItem> wrapper1 = new UpdateWrapper<>();
        wrapper1.lambda().eq(BizReceiptContractSubItem::getReceiveId, headDTO.getId());
        bizReceiptContractSubItemDataWrap.physicalDelete(wrapper1);

        UpdateWrapper<BizReceiptContractAttendanceItem> wrapper2 = new UpdateWrapper<>();
        wrapper2.lambda().eq(BizReceiptContractAttendanceItem::getHeadId, headDTO.getId());
        bizReceiptContractAttendanceItemDataWrap.physicalDelete(wrapper2);

        UpdateWrapper<BizReceiptContractExamineItem> wrapper3 = new UpdateWrapper<>();
        wrapper3.lambda().eq(BizReceiptContractExamineItem::getHeadId, headDTO.getId());
        bizReceiptContractExamineItemDataWrap.physicalDelete(wrapper3);

        // 删除柴油复核行项目
        UpdateWrapper<BizReceiptContractDieselRecheckItem> wrapper4 = new UpdateWrapper<>();
        wrapper4.lambda().eq(BizReceiptContractDieselRecheckItem::getHeadId, headDTO.getId());
        bizReceiptContractDieselRecheckItemDataWrap.physicalDelete(wrapper4);
    }

    /**
     * 保存单据流
     *
     * @in ctx 入参 {@link BizReceiptContractReceivingHeadDTO : "要保持单据流的申请单"}
     */
    public void saveReceiptTree(BizContext ctx) {
        // 入参上下文 - 要保持单据流的申请单
        BizReceiptContractReceivingHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizCommonReceiptRelation> dtoList = new ArrayList<>();
        for (BizReceiptContractReceivingItemDTO item : headDTO.getItemList()) {
            BizCommonReceiptRelation dto = new BizCommonReceiptRelation()
                    .setReceiptType(headDTO.getReceiptType())
                    .setReceiptHeadId(item.getHeadId())
                    .setReceiptItemId(item.getId())
                    .setPreReceiptType(item.getPreReceiptType())
                    .setPreReceiptHeadId(item.getPreReceiptHeadId())
                    .setPreReceiptItemId(item.getPreReceiptItemId());
            dtoList.add(dto);
        }
        if (UtilCollection.isNotEmpty(dtoList)) {
            receiptRelationService.multiSaveReceiptTree(dtoList);
        }
    }

    /**
     * 保存操作日志
     *
     * @param ctx ctx
     */
    public void saveBizReceiptOperationLog(BizContext ctx) {
        // 入参上下文 - 保存的服务工程确认
        BizReceiptContractReceivingHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 保存操作日志
        receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(), operationLogType, "", ctx.getCurrentUser().getId());
    }

    /**
     * 保存附件
     */
    public void saveBizReceiptAttachment(BizContext ctx) {
        // 入参上下文
        BizReceiptContractReceivingHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        // 保存服务工程确认附件
        receiptAttachmentService.saveBizReceiptAttachment(headDTO.getFileList(), headDTO.getId(), EnumReceiptType.CONTRACT_CONFIRM.getValue(), user.getId());
        // 保存考勤确认、合同考核确认、柴油复核确认行项目附件
        for (BizReceiptContractAttendanceItemDTO attendanceItemDTO : headDTO.getAttendanceItemList()) {
            receiptAttachmentService.saveBizReceiptAttachment(attendanceItemDTO.getFileList(), headDTO.getId(), attendanceItemDTO.getId(), EnumReceiptType.CONTRACT_CONFIRM.getValue(), user.getId());
        }
        for (BizReceiptContractExamineItemDTO examineItemDTO : headDTO.getExamineItemList()) {
            receiptAttachmentService.saveBizReceiptAttachment(examineItemDTO.getFileList(), headDTO.getId(), examineItemDTO.getId(), EnumReceiptType.CONTRACT_CONFIRM.getValue(), user.getId());
        }
        for (BizReceiptContractDieselRecheckItemDTO dieselRecheckItemDTO : headDTO.getDieselRecheckItemList()) {
            receiptAttachmentService.saveBizReceiptAttachment(dieselRecheckItemDTO.getFileList(), headDTO.getId(), dieselRecheckItemDTO.getId(), EnumReceiptType.CONTRACT_CONFIRM.getValue(), user.getId());
        }
        log.debug("保存服务工程确认附件成功!");
    }

    /**
     * 提交单据
     *
     * @param ctx ctx
     */
    public void submit(BizContext ctx) {
        // 入参上下文
        BizReceiptContractReceivingHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT);
        // 提交时间、提交人
        po.setSubmitTime(UtilDate.getNow());
        po.setSubmitUserId(ctx.getCurrentUser().getId());
        // 保存服务工程确认
        this.save(ctx);
    }

    /**
     * 发起审批
     *
     * @in ctx 入参 {@link BizReceiptApplyHeadDTO : "申请单"}
     */
    public void startWorkFlow(BizContext ctx) {
        BizReceiptContractReceivingHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        // 发起流程审批
        Long receiptId = headDTO.getId();
        String receiptCode = headDTO.getReceiptCode();
        Integer receiptType = headDTO.getReceiptType();
        Map<String, Object> variables = new HashMap<>();

        // 审批人校验
        this.approveCheck(variables, headDTO);

        // 物料编码申请：“请审批”[公司+部门]用户姓名+“提交的流程”+申请原因（取物料编码申请抬头申请原因）
        variables.put("subject", "请审批[" + dictionaryService.getCorpCacheById(ctx.getCurrentUser().getCorpId()).getCorpName() + ctx.getCurrentUser().getUserDeptList().get(0).getDeptName() + "]" + ctx.getCurrentUser().getUserName() + "提交的流程：" + headDTO.getRemark());

        workflowService.setBizReceiptVariables(variables, receiptId, receiptCode, receiptType, ctx, headDTO.getDescription());
        workflowService.startWorkFlow(receiptId, receiptCode, receiptType, variables);

        // 更新物料编码申请head - 审核中
        this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue());

        // 如果是驳回之后的再次提交，那驳回的时候给单据提交人发送了待办，因此在提交时，需要完成待办
        hXOaIntegerfaceService.completeTodo(HXOaIntegerfaceService.SEND_TYPE_DEFAULT, headDTO.getId().toString(), Arrays.asList(UtilCurrentContext.getCurrentUser().getUserCode()), headDTO.getReceiptCode());
    }

    /**
     * 审批人校验
     *
     * @param variables 待办变量
     * @param headDTO   单据
     */
    private void approveCheck(Map<String, Object> variables, BizReceiptContractReceivingHeadDTO headDTO) {
        // 服务工程确认单审批：开始-专工(单据抬头)-专工所属部门领导-结束
        // 二级审批节点 - 专工
        SysUser level1SysUser = sysUserDataWrap.getById(headDTO.getAssignUserId());
        if (UtilObject.isNull(level1SysUser)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "1");
        }
        variables.put("level1ApproveUserCode", level1SysUser.getUserCode());
        // 三级审批节点 - 专工所属部门领导
        List<String> level2UserList = new ArrayList<>();
        List<MetaDataDeptOfficePO> assignUserDeptList = sysUserDeptOfficeRelDataWrap.getUserDept(headDTO.getAssignUserId());
        for (MetaDataDeptOfficePO deptOffice : assignUserDeptList) {
            level2UserList.addAll(sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptOffice.getDeptCode(), null, EnumApprovalLevel.LEVEL_2));
        }
        if (UtilCollection.isEmpty(level2UserList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "2");
        }
        variables.put("userDept", assignUserDeptList);
    }

    /**
     * 审批回调
     *
     * @in ctx 入参 {@link BizApprovalReceiptInstanceRelDTO ："回调参数"}
     */
    public void approvalCallback(BizApprovalReceiptInstanceRelDTO wfReceiptCo) {
        BizContext ctx = new BizContext();
        CurrentUser currentUser = wfReceiptCo.getInitiator();
        ctx.setCurrentUser(currentUser);
        BizReceiptContractReceivingHead head = bizReceiptContractReceivingHeadDataWrap.getById(wfReceiptCo.getReceiptHeadId());
        BizReceiptContractReceivingHeadDTO headDTO = UtilBean.newInstance(head, BizReceiptContractReceivingHeadDTO.class);
        dataFillService.fillAttr(headDTO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_REF_PO, headDTO);
        if (wfReceiptCo.getApproveStatus().equals(EnumApprovalStatus.FINISH.getValue())) {

            // 更新状态已完成
            this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());

            // 生成下游单据 合同收货
            this.genContractReceiving(ctx);
        } else {

            // 如果驳回时携带了废弃标记，则直接关闭单据
            if (EnumRealYn.TRUE.getIntValue().equals(wfReceiptCo.getIsDiscard())) {
                this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_CLOSED.getValue());

                // 被废弃后发送待办给单据提交人进行提醒
                hXOaIntegerfaceService.sendTodo(HXOaIntegerfaceService.SEND_TYPE_DEFAULT, StringUtils.join("请查看", head.getReceiptCode(), "服务工程确认的审批废弃"), StringUtils.EMPTY, wfReceiptCo.getReceiptHeadId().toString(), Arrays.asList(dictionaryService.getSysUserCacheById(head.getSubmitUserId()).getUserCode()), head.getReceiptCode());
            } else {
                // 更新状态已驳回
                this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue());

                // 被驳回后发送待办给单据提交人进行提醒
                hXOaIntegerfaceService.sendTodo(HXOaIntegerfaceService.SEND_TYPE_DEFAULT, StringUtils.join("请查看", head.getReceiptCode(), "服务工程确认的审批驳回"), StringUtils.EMPTY, wfReceiptCo.getReceiptHeadId().toString(), Arrays.asList(dictionaryService.getSysUserCacheById(head.getSubmitUserId()).getUserCode()), head.getReceiptCode());
            }
        }
    }

    /**
     * 更新服务工程确认状态
     *
     * @param headDTO     服务工程确认head
     * @param itemDTOList 服务工程确认item
     */
    public void updateStatus(BizReceiptContractReceivingHeadDTO headDTO, List<BizReceiptContractReceivingItemDTO> itemDTOList, Integer status) {
        if (UtilObject.isNull(headDTO)) {
            // 更新item状态
            itemDTOList.forEach(item -> item.setItemStatus(status));
            this.updateItem(itemDTOList);
        } else if (UtilCollection.isEmpty(itemDTOList)) {
            // 更新head状态
            headDTO.setReceiptStatus(status);
            this.updateHead(headDTO);
        } else if (UtilCollection.isNotEmpty(itemDTOList)) {
            // 更新head、item状态
            itemDTOList.forEach(item -> item.setItemStatus(status));
            this.updateItem(itemDTOList);
            headDTO.setReceiptStatus(status);
            this.updateHead(headDTO);
        }
    }

    /**
     * 更新服务工程确认head状态
     *
     * @param headDto 服务工程确认head
     */
    private void updateHead(BizReceiptContractReceivingHeadDTO headDto) {
        if (UtilObject.isNotNull(headDto)) {
            bizReceiptContractReceivingHeadDataWrap.updateDtoById(headDto);
        }
    }

    /**
     * 更新服务工程确认item状态
     *
     * @param itemDtoList 服务工程确认item
     */
    private void updateItem(List<BizReceiptContractReceivingItemDTO> itemDtoList) {
        if (UtilCollection.isNotEmpty(itemDtoList)) {
            bizReceiptContractReceivingItemDataWrap.updateBatchDtoById(itemDtoList);
        }
    }

    /**
     * 生成合同收货
     *
     * @param ctx 入参上下文
     */
    public void genContractReceiving(BizContext ctx) {
        // 入参上下文 - 收货单
        BizReceiptContractReceivingHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_REF_PO);
        ctx.setPoContextData(headDTO);
        // 设置上下文单据日志 - 创建
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_ESTABLISH);

        // 生成合同收货单
        for (BizReceiptContractReceivingItemDTO itemDTO : headDTO.getItemList()) {
            itemDTO.setPreReceiptType(headDTO.getReceiptType());
            itemDTO.setPreReceiptItemId(itemDTO.getId());
            itemDTO.setPreReceiptHeadId(headDTO.getId());
            itemDTO.setId(null);
            itemDTO.setHeadId(null);
        }
        headDTO.setId(null);
        if (headDTO.getReceiveType() != 2) {
            headDTO.setAttendanceItemList(new ArrayList<>());
            headDTO.setExamineItemList(new ArrayList<>());
            headDTO.setDieselRecheckItemList(new ArrayList<>());
        }
        contractReceivingComponent.save(ctx);
    }

    /**
     * 撤销
     */
    public void revoke(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizReceiptContractReceivingHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 单据状态 - 草稿
        this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
        // 删除待办:必须在审批撤销前
        String id = workflowService.deleteTodo(headDTO.getId());
        // 审批撤销
        RevokeDTO revokeDTO = new RevokeDTO();
        revokeDTO.setProcessInstanceId(id);
        workflowService.revoke(revokeDTO);
        receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(), EnumReceiptOperationType.RECEIPT_OPERATION_REVOKE, "", user.getId());
    }

    /**
     * 删除
     */
    public void delete(BizContext ctx) {
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        BizReceiptContractReceivingHead head = bizReceiptContractReceivingHeadDataWrap.getById(headId);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, head.getReceiptCode());
        BizReceiptContractReceivingHeadDTO headDTO = UtilBean.newInstance(head, BizReceiptContractReceivingHeadDTO.class);
        dataFillService.fillAttr(headDTO);
        bizReceiptContractReceivingItemDataWrap.removeByIds(headDTO.getItemList().stream().map(BizReceiptContractReceivingItemDTO::getId).collect(Collectors.toList()));
        bizReceiptContractReceivingHeadDataWrap.removeById(headId);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
    }

    /**
     * 逻辑删除附件
     */
    public void deleteBizReceiptAttachment(BizContext ctx) {
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        // 逻辑删除附件
        receiptAttachmentService.deleteBizReceiptAttachment(headId, EnumReceiptType.CONTRACT_CONFIRM.getValue());
    }

    /**
     * 冲销行项目校验
     *
     * @param itemDTO BizInspectInputItemDTO
     * @return true/false
     */
    public boolean itemCanWriteOff(BizReceiptContractReceivingItemDTO itemDTO) {
        return (EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(itemDTO.getItemStatus()));
    }

    /**
     * 过账前数据校验
     */
    public void checkPost(BizContext ctx) {
        // 入参上下文
        BizReceiptContractReceivingHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 获取收货单
        BizReceiptContractReceivingHead head = bizReceiptContractReceivingHeadDataWrap.getById(po.getId());
        // 转DTO
        BizReceiptContractReceivingHeadDTO headDTO = UtilBean.newInstance(head, BizReceiptContractReceivingHeadDTO.class);
        // 填充关联属性及父子属性
        dataFillService.fillAttr(headDTO);
        // 校验可过账的状态
        Set<Integer> canRePostStatusSet = new HashSet<>();
        canRePostStatusSet.add(EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
        if (!canRePostStatusSet.contains(head.getReceiptStatus())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_NO_AUTHORITY_POST);
        }
    }

}
