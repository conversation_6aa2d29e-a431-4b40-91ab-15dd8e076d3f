package com.inossem.wms.bizdomain.output.controller;

import com.inossem.wms.bizdomain.output.service.biz.MaterialOutputService;
import com.inossem.wms.common.annotation.RepeatSubmit;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.apply.EnumReceiveType;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleRuleDTO;
import com.inossem.wms.common.model.bizdomain.common.receipt.po.ReceiptItemActionPO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputHeadDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.MatStockDTO;
import com.inossem.wms.common.model.bizdomain.output.po.BizReceiptOutputQueryListPO;
import com.inossem.wms.common.model.bizdomain.output.po.BizReceiptOutputSearchPO;
import com.inossem.wms.common.model.bizdomain.output.vo.BizReceiptOutputPageVO;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> wang
 *@description 领料出库单创建
 * @date 2022/4/24 13:24
 */
@RestController
@Slf4j
public class MaterialOutputController {

    @Autowired
    private MaterialOutputService materialOutputService;

    @ApiOperation(value = "领料出库创建初始化-sdw", tags = {"出库管理-领料出库sdw"})
    @PostMapping(value = "/outputs/mat-output/init")
    public BaseResult<BizResultVO<BizReceiptOutputHeadDTO>> init(BizContext ctx) {
        materialOutputService.init(ctx);
        BizResultVO<BizReceiptOutputHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }



    @ApiOperation(value = "领料出库查询列表（分页）-sdw", tags = {"出库管理-领料出库sdw"})
    @PostMapping(value = "/outputs/mat-output/results")
    public BaseResult<PageObjectVO<BizReceiptOutputPageVO>> getPage(@RequestBody BizReceiptOutputQueryListPO po, BizContext ctx) {
        materialOutputService.getPage(ctx);
        PageObjectVO<BizReceiptOutputPageVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "领料出库查询物料库存-sdw",tags = {"出库管理-领料出库sdw"})
    @PostMapping("/outputs/mat-out/mat-stock/list")
    public BaseResult<MatStockDTO> getMatStockInfo(@RequestBody BizReceiptOutputSearchPO po,BizContext ctx){
        materialOutputService.getMatStockInfo(ctx);
        MatStockDTO dto = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(dto);
    }

    @ApiOperation(value = "领料出库详情-sdw", tags = {"出库管理-领料出库sdw"})
    @GetMapping(value = "/outputs/mat-output/{id}")
    public BaseResult<BizResultVO<BizReceiptOutputHeadDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        materialOutputService.getInfo(ctx);
        BizResultVO<BizReceiptOutputHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 领料出库-处理
     */
    @ApiOperation(value = "领料出库-处理", tags = {"出库管理-领料出库sdw"})
    @PostMapping(value = "/outputs/mat-output/deal", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> deal(@RequestBody BizReceiptOutputHeadDTO po, BizContext ctx) {
        materialOutputService.deal(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_OUTPUT_SUBMIT_SUCCESS, code);
    }

    @ApiOperation(value = "领料出库详情打印-sdw", tags = {"出库管理-领料出库sdw"})
    @GetMapping(value = "/outputs/mat-output/print/{id}")
    public BaseResult<BizResultVO<BizReceiptOutputHeadDTO>> printInfo(@PathVariable("id") Long id, BizContext ctx) {
        materialOutputService.printInfo(ctx);
        BizResultVO<BizReceiptOutputHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 暂时对外不使用，领料出库单由预留单创建成功后自动创建出库单并保存
     * @param po
     * @param ctx
     * @return
     */
    @ApiOperation(value = "领料出库保存-sdw", tags = {"出库管理-领料出库sdw"})
    @PostMapping(value = "/outputs/mat-output/save")
    public BaseResult save(@RequestBody BizReceiptOutputHeadDTO po, BizContext ctx) {
        materialOutputService.save(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_OUTPUT_SAVE_SUCCESS, code);
    }
    @ApiOperation(value = "领料出库单提交-sdw", tags = {"出库管理-领料出库sdw"})
    @PostMapping(value = "/outputs/mat-output/submit")
    public BaseResult submit(@RequestBody BizReceiptOutputHeadDTO po, BizContext ctx) {
        materialOutputService.submit(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_OUTPUT_SUBMIT_SUCCESS, code);
    }


    @ApiOperation(value = "领料出库单获取配货信息", tags = {"出库管理-sdw"})
    @PostMapping(value = "/outputs/mat-output/item-info")
    public BaseResult<BizReceiptAssembleRuleDTO> getItemInfo(@RequestBody BizReceiptOutputSearchPO po, BizContext ctx) {
        materialOutputService.getItemInfo(ctx);
        BizReceiptAssembleRuleDTO assembleRuleDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(assembleRuleDTO);
    }


    @ApiOperation(value = "领料出库过账-sdw", tags = {"出库管理-领料出库sdw"})
    @RepeatSubmit
    @PostMapping(value = "/outputs/mat-output/post")
    public BaseResult post(@RequestBody BizReceiptOutputHeadDTO po, BizContext ctx) {
        if (po!=null  && EnumReceiveType.PRODUCE_REQ_USE.getValue().equals(po.getReceiveType())) { //需求计划领用
            log.debug("单据{}领用类型{}不需关闭预留", po.getReceiptCode(), po.getReceiveType());
            materialOutputService.reqPlanPost(ctx);
        }else{
            log.debug("单据{}领用类型{}需要关闭预留", po.getReceiptCode(), po.getReceiveType());
            materialOutputService.post(ctx);
        }
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_OUTPUT_POST_SUCCESS, po.getReceiptCode());
    }

    @ApiOperation(value = "领料出库关闭预留-sdw", tags = {"出库管理-领料出库sdw"})
    @PostMapping(value = "/outputs/mat-output/close")
    public BaseResult close(@RequestBody BizReceiptOutputHeadDTO po, BizContext ctx) {
        if (po!=null  && EnumReceiveType.PRODUCE_REQ_USE.getValue().equals(po.getReceiveType())) { //需求计划领用
            materialOutputService.reqPlanClose(ctx);
        }else{
            materialOutputService.close(ctx);
        }
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_OUTPUT_CLOSE_SUCCESS, po.getReceiptCode());
    }

    @ApiOperation(value = "领料出库冲销-sdw", tags = {"出库管理-领料出库sdw"})
    @PostMapping(value = "/outputs/mat-output/write-off")
    public BaseResult writeOff(@RequestBody ReceiptItemActionPO po, BizContext ctx) {
        materialOutputService.writeOff(ctx);
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_OUTPUT_WRITEOFF_SUCCESS, headDTO.getReceiptCode());
    }

    @ApiOperation(value = "领料出库删除-sdw", tags = {"出库管理-领料出库"})
    @DeleteMapping(value = "/outputs/mat-output/{id}")
    public BaseResult delete(@PathVariable("id") Long id, BizContext ctx) {
        materialOutputService.delete(ctx);
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_OUTPUT_DELETE_SUCCESS, headDTO.getReceiptCode());
    }

    @ApiOperation(value = "领料出库修改行项目备注", tags = {"出库管理-领料出库"})
    @PostMapping(value = "/outputs/mat-output/item-remark")
    public BaseResult saveItemRemark(@RequestBody ReceiptItemActionPO po, BizContext ctx) {
        materialOutputService.saveItemRemark(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_OUTPUT_SAVE_SUCCESS, po.getReceiptCode());
    }

    @ApiOperation(value = "领料出库关闭预留", tags = {"出库管理-领料出库"})
    @PostMapping(value = "/outputs/mat-output/close-expired")
    public BaseResult closeExpired() {
        materialOutputService.closeReserveReceiptByScheduleJob();
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS);
    }

}
