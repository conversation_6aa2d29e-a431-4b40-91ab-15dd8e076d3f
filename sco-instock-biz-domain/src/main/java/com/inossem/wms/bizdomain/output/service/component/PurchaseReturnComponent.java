package com.inossem.wms.bizdomain.output.service.component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptRelationService;
import com.inossem.wms.bizbasis.erp.service.biz.PurchaseReceiptService;
import com.inossem.wms.bizbasis.feign.input.InputFeignApi;
import com.inossem.wms.bizbasis.feign.inspect.InspectFeignApi;
import com.inossem.wms.bizbasis.sap.restful.service.HXSapIntegerfaceService;
import com.inossem.wms.bizdomain.output.service.component.movetype.InsPurchaseReturnMoveTypeComponent;
import com.inossem.wms.bizdomain.output.service.component.movetype.InsPurchaseReturnWriteOffMoveTypeComponent;
import com.inossem.wms.bizdomain.output.service.datawrap.BizReceiptOutputBinDataWrap;
import com.inossem.wms.bizdomain.output.service.datawrap.BizReceiptOutputHeadDataWrap;
import com.inossem.wms.bizdomain.output.service.datawrap.BizReceiptOutputItemDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumMoveType;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptRelation;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputItemDTO;
import com.inossem.wms.common.model.bizdomain.inspect.dto.BizReceiptInspectItemDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputBinDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputHeadDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputItemDTO;
import com.inossem.wms.common.model.bizdomain.output.vo.BizReceiptOutputPreVO;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.ButtonVO;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.erp.dto.ErpPurchaseReceiptItemDTO;
import com.inossem.wms.common.model.erp.po.BizReceiptPreSearchPO;
import com.inossem.wms.common.model.erp.po.PreReceiptQueryPO;
import com.inossem.wms.common.model.sap.posting.HXPostingHeader;
import com.inossem.wms.common.model.sap.posting.HXPostingItem;
import com.inossem.wms.common.model.sap.posting.HXPostingReturn;
import com.inossem.wms.common.model.stock.dto.StockInsMoveTypeDTO;
import com.inossem.wms.common.model.stock.dto.StockInsMoveTypePostTaskDTO;
import com.inossem.wms.common.model.stock.entity.StockInsDocBatch;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilBigDecimal;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilConst;
import com.inossem.wms.common.util.UtilDate;
import com.inossem.wms.common.util.UtilLocalDateTime;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;

import lombok.extern.slf4j.Slf4j;

/**
 * 采购退货组件类
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2021-05-11
 */

@Service
@Slf4j
public class PurchaseReturnComponent {

    @Autowired
    protected DataFillService dataFillService;

    @Autowired
    private BizReceiptOutputHeadDataWrap bizReceiptOutputHeadDataWrap;

    @Autowired
    private BizCommonService bizCommonService;

    @Autowired
    private PurchaseReceiptService purchaseReceiptService;

    @Autowired
    private OutputComponent outputComponent;

    @Autowired
    private ReceiptRelationService receiptRelationService;

    @Autowired
    private InsPurchaseReturnMoveTypeComponent insPurchaseReturnMoveTypeComponent;

    @Autowired
    private InsPurchaseReturnWriteOffMoveTypeComponent insPurchaseReturnWriteOffMoveTypeComponent;

    @Autowired
    private InputFeignApi inputFeignApi;

    @Autowired
    private InspectFeignApi inspectFeignApi;

    @Autowired
    private HXSapIntegerfaceService hxInterfaceService;

    @Autowired
    private BizReceiptOutputItemDataWrap bizReceiptOutputItemDataWrap;

    @Autowired
    private BizReceiptOutputBinDataWrap bizReceiptOutputBinDataWrap;

    /**
     * 设置可用数量
     *
     * @param itemList 行项目列表
     */
    public void setAvailableQty(List<BizReceiptOutputItemDTO> itemList) {
        List<BizReceiptOutputItemDTO> qtyList = this.getWmsCreatedQtyByList(itemList);
        for (BizReceiptOutputItemDTO item : itemList) {
            BizReceiptOutputItemDTO qtyItemVo = qtyList.stream()
                .filter(q -> q.getPreReceiptType().equals(item.getPreReceiptType())
                    && q.getPreReceiptItemId().equals(item.getPreReceiptItemId()))
                .findFirst().orElse(new BizReceiptOutputItemDTO());
            BigDecimal createdQty = UtilObject.getBigDecimalOrZero(qtyItemVo.getCreatedQty());
            BigDecimal receiptQty = UtilObject.getBigDecimalOrZero(item.getReceiptQty());
            BigDecimal submitQty = UtilObject.getBigDecimalOrZero(item.getSubmitQty());
            // 可用数量 = SAP订单数量 - SAP已发货数量 - wms已创建但未过账的出库单数量
            BigDecimal availableQty = receiptQty.subtract(submitQty).subtract(createdQty);
            item.setAvailableQty(availableQty);
        }
    }

    /**
     * 设置已发货数量
     *
     * @param itemDTOList 行项目列表
     * @param user 用户
     */
    public void setSubmitQty(List<BizReceiptOutputItemDTO> itemDTOList, CurrentUser user) {
        BizReceiptPreSearchPO po = new BizReceiptPreSearchPO();
        po.setPurchaseReceiptCode(itemDTOList.get(0).getPreReceiptCode());
        List<ErpPurchaseReceiptItemDTO> reserveReceiptItemList =
            purchaseReceiptService.getErpPurchaseReceiptItemList(po, user);
        for (BizReceiptOutputItemDTO item : itemDTOList) {
            for (ErpPurchaseReceiptItemDTO erpPurchaseReceiptItemDTO : reserveReceiptItemList) {
                if (item.getPreReceiptItemId().equals(erpPurchaseReceiptItemDTO.getId())) {
                    item.setSubmitQty(erpPurchaseReceiptItemDTO.getSubmitQty());
                    break;
                }
            }
        }
    }

    /**
     * 获取行项目的wms已创建未同步SAP的数量
     *
     * @param itemList 行项目list
     * @return 数量列表
     */
    public List<BizReceiptOutputItemDTO> getWmsCreatedQtyByList(List<BizReceiptOutputItemDTO> itemList) {
        if (UtilCollection.isNotEmpty(itemList)) {
            // 查询单据状态大于10， 并且SAP物料凭证为空的，具有相同前序单号的其他的出库单的出库数
            return bizReceiptOutputHeadDataWrap.getWmsCreatedQtyByList(itemList.get(0).getHeadId(), itemList);
        } else {
            return new ArrayList<>();
        }
    }

    /**
     * 提交校验
     *
     * @param ctx 上下文
     */
    public void checkSubmit(BizContext ctx) {
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 参数基本校验
        outputComponent.check(ctx);
        // 出库数量是否为0校验
        outputComponent.checkItemQtyIsZero(ctx);
        // 出库数是否小于可出库数
        //this.checkOperatedQty(headDTO);
        // 校验单据状态
        outputComponent.checkReceiptStatus(ctx);
    }

    /**
     * 出库数是否小于可出库数
     *
     * @param headDTO headDTO
     */
    public void checkOperatedQty(BizReceiptOutputHeadDTO headDTO) {
        // 出库数量大于可出库数量时抛出异常
        this.setSubmitQty(headDTO.getItemDTOList(), null);
        this.setAvailableQty(headDTO.getItemDTOList());
        for (BizReceiptOutputItemDTO itemDTO : headDTO.getItemDTOList()) {
            BigDecimal qty = BigDecimal.ZERO;
            // 计算单个行项目的出库总数
            if (itemDTO.getAssembleDTOList() != null && itemDTO.getAssembleDTOList().size() > 0) {
                for (BizReceiptAssembleDTO assembleDTO : itemDTO.getAssembleDTOList()) {
                    qty = qty.add(assembleDTO.getQty());
                }
            }
            if (qty.compareTo(itemDTO.getAvailableQty()) > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_QUANTITY_DIFFERENCES);
            }
        }
    }

    /**
     * 获取过账移动类型并校验
     *
     * @param ctx 上下文
     */
    public void generateInsMoveTypeAndCheck(BizContext ctx) {
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        StockInsMoveTypePostTaskDTO stockInsMoveTypePostTaskDTO = new StockInsMoveTypePostTaskDTO();
        StockInsMoveTypeDTO postingInsMoveTypeDTO;
        try {
            postingInsMoveTypeDTO = insPurchaseReturnMoveTypeComponent.generatePostingInsDoc(headDTO);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new WmsException(EnumReturnMsg.RETURN_CODE_GET_INS_MOVE_TYPE_EXCEPTION);
        }
        // 生成凭证code
        bizCommonService.setInsDocCode(postingInsMoveTypeDTO);
        // 过账凭证(处理批次库存、临时区仓位库存)
        stockInsMoveTypePostTaskDTO.setPostDTO(postingInsMoveTypeDTO);
        // 作业凭证(处理临时区仓位库存、实际仓位库存)
        stockInsMoveTypePostTaskDTO.setTaskDTO(null);
        outputComponent.checkAndComputeForModifyStock(stockInsMoveTypePostTaskDTO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_INS, stockInsMoveTypePostTaskDTO);
    }

    /**
     * 非先过账模式-获取冲销移动类型并校验
     *
     * @param ctx 上下文
     *
     */
    public void generateWriteOffInsMoveTypeAndCheckNonPostFirst(BizContext ctx) {
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        StockInsMoveTypePostTaskDTO stockInsMoveTypePostTaskDTO = this.setWriteOffMoveTypeNonPostFirst(headDTO);
        outputComponent.checkAndComputeForModifyStock(stockInsMoveTypePostTaskDTO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_INS, stockInsMoveTypePostTaskDTO);
    }

    private StockInsMoveTypePostTaskDTO setWriteOffMoveTypeNonPostFirst(BizReceiptOutputHeadDTO headDTO) {
        StockInsMoveTypeDTO postingInsMoveTypeVo;
        StockInsMoveTypeDTO taskInsMoveTypeVo = null;
        try {
            postingInsMoveTypeVo = insPurchaseReturnWriteOffMoveTypeComponent.generatePostingInsDocNonPostFirst(headDTO);
            List<BizReceiptOutputItemDTO> itemDTOList = headDTO.getItemDTOList();
            boolean isGenerateTaskInsDoc = false;
            for (BizReceiptOutputItemDTO itemDTO : itemDTOList) {
                if (UtilCollection.isNotEmpty(itemDTO.getBinDTOList())) {
                    isGenerateTaskInsDoc = true;
                    break;
                }
            }
            // 非同时模式判断是否生成task凭证
            if (isGenerateTaskInsDoc) {
                taskInsMoveTypeVo = insPurchaseReturnWriteOffMoveTypeComponent.generateTaskInsDoc(headDTO);
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new WmsException(EnumReturnMsg.RETURN_CODE_GET_INS_MOVE_TYPE_EXCEPTION);
        }
        // 生成凭证code
        bizCommonService.setInsDocCode(postingInsMoveTypeVo);
        bizCommonService.setInsDocCode(taskInsMoveTypeVo);
        StockInsMoveTypePostTaskDTO dto = new StockInsMoveTypePostTaskDTO();
        // 过账凭证(处理批次库存、临时区仓位库存)
        dto.setPostDTO(postingInsMoveTypeVo);
        // 作业凭证(处理临时区仓位库存、实际仓位库存)
        dto.setTaskDTO(taskInsMoveTypeVo);
        return dto;
    }

    /**
     * 校验删除
     *
     * @param ctx 上下文
     */
    public void checkDelete(BizContext ctx) {
        Long id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilObject.isNull(id)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 判断出库单是否已同步SAP
        boolean syncStatus = outputComponent.getSapPostSyncStatus(id);
        if (syncStatus) {
            log.warn("出库单{}删除状态校验，失败，出库单已同步SAP，存在SAP过账物料凭证", id);
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_STATUS_FALSE);
        }
    }

    /**
     * 获取详情
     *
     * @param ctx 上下文
     */
    public void getInfo(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        BizReceiptOutputHeadDTO headDTO = outputComponent.getItemListById(headId);
        List<BizReceiptOutputItemDTO> itemDTOList = headDTO.getItemDTOList();
        BizReceiptOutputItemDTO bizReceiptOutputItemDTO = itemDTOList.get(0);
        if (Objects.nonNull(bizReceiptOutputItemDTO)) {
            headDTO.setDocDate(bizReceiptOutputItemDTO.getDocDate());
            headDTO.setPostingDate(bizReceiptOutputItemDTO.getPostingDate());
            headDTO.setMatDocCode(bizReceiptOutputItemDTO.getMatDocCode());
            for (BizReceiptOutputItemDTO itemDTO : itemDTOList) {
                if (EnumReceiptType.PURCHASE_INSPECTION.getValue().equals(itemDTO.getPreReceiptType())
                    || EnumReceiptType.STOCK_INPUT_PURCHASE.getValue().equals(itemDTO.getPreReceiptType())) {
                    itemDTO.setReceiptQty(itemDTO.getPreReceiptQty());
                }
            }
        }
        // 已发货数量
        //this.setSubmitQty(itemDTOList, user);
        // 设置物料工厂信息(是否启用erp批次生产批次启用包装)
        outputComponent.setMaterialFactoryInfo(itemDTOList);
        // 可用数量
        //this.setAvailableQty(itemDTOList);
        // 设置按钮
        ButtonVO button = outputComponent.setButton(headId);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(headDTO, new ExtendVO(), button));
    }

    /**
     * 保存单据流
     *
     * @param ctx 上下文
     */
    public void saveReceiptTree(BizContext ctx) {
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptOutputItemDTO> itemDTOList = headDTO.getItemDTOList();
        List<BizCommonReceiptRelation> list = new ArrayList<>();
        for (BizReceiptOutputItemDTO item : itemDTOList) {
            BizCommonReceiptRelation bizCommonReceiptRelation = new BizCommonReceiptRelation();
            bizCommonReceiptRelation.setReceiptType(EnumReceiptType.STOCK_OUTPUT_PURCHASE_RETURN.getValue());
            bizCommonReceiptRelation.setReceiptHeadId(item.getHeadId());
            bizCommonReceiptRelation.setReceiptItemId(item.getId());
            bizCommonReceiptRelation.setPreReceiptType(item.getPreReceiptType());
            bizCommonReceiptRelation.setPreReceiptHeadId(item.getPreReceiptHeadId());
            bizCommonReceiptRelation.setPreReceiptItemId(item.getPreReceiptItemId());
            list.add(bizCommonReceiptRelation);
        }
        receiptRelationService.multiSaveReceiptTree(list);
    }

    /**
     * 逻辑删除单据流
     *
     * @param ctx 上下文
     */
    public void deleteReceiptTree(BizContext ctx) {
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        receiptRelationService.deleteReceiptTree(EnumReceiptType.STOCK_OUTPUT_PURCHASE_RETURN.getValue(), headId);
    }

    /**
     * 查询采购订单行项目列表
     *
     * @param ctx 上下文
     */
    public void getPurchaseReceiptItemList(BizContext ctx) {
        // 入参上下文
        BizReceiptPreSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 查询采购退货订单
        po.setIsReturnFlag(EnumRealYn.TRUE.getIntValue());
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        // 调用SAP查询采购订单
        List<ErpPurchaseReceiptItemDTO> purchaseReceiptItemVoList =
            purchaseReceiptService.getErpPurchaseReceiptItemList(po, user);
        if (UtilCollection.isEmpty(purchaseReceiptItemVoList)) {
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>());
            return;
        }
        // 组装数据
        List<BizReceiptOutputPreVO> bizReceiptOutputPreVOList = new ArrayList<>();
        Map<String, List<ErpPurchaseReceiptItemDTO>> reserveMap = purchaseReceiptItemVoList.stream()
            .collect(Collectors.groupingBy(ErpPurchaseReceiptItemDTO::getReceiptCode));
        for (Map.Entry<String, List<ErpPurchaseReceiptItemDTO>> entry : reserveMap.entrySet()) {
            BizReceiptOutputPreVO bizReceiptOutputPreVO = new BizReceiptOutputPreVO();
            List<BizReceiptOutputItemDTO> itemDTOList = new ArrayList<>();
            for (ErpPurchaseReceiptItemDTO erpItemDTO : entry.getValue()) {
                BizReceiptOutputItemDTO itemDTO = UtilBean.newInstance(erpItemDTO, BizReceiptOutputItemDTO.class);
                itemDTO.setId(null);
                itemDTO.setHeadId(null);
                itemDTO.setReceiptType(EnumReceiptType.STOCK_OUTPUT_PURCHASE_RETURN.getValue());
                itemDTO.setPreReceiptHeadId(erpItemDTO.getHeadId());
                itemDTO.setPreReceiptItemId(erpItemDTO.getId());
                itemDTO.setPreReceiptType(EnumReceiptType.PURCHASE_RECEIPT.getValue());
                itemDTO.setPreReceiptRid(erpItemDTO.getRid());
                itemDTO.setPreReceiptCode(entry.getKey());
                itemDTO.setPreReceiptQty(erpItemDTO.getReceiptQty());
                itemDTO.setReferReceiptHeadId(erpItemDTO.getHeadId());
                itemDTO.setReferReceiptItemId(erpItemDTO.getId());
                itemDTO.setReferReceiptType(EnumReceiptType.PURCHASE_RECEIPT.getValue());
                itemDTO.setErpCreateUserName(erpItemDTO.getErpCreateUserName());
                itemDTO.setErpCreateTime(erpItemDTO.getErpCreateTime());
                itemDTO.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue());
                itemDTOList.add(itemDTO);
            }
            // 设置物料工厂信息(是否启用erp批次生产批次启用包装)
            outputComponent.setMaterialFactoryInfo(itemDTOList);
            // 设置可用数量
            this.setAvailableQty(itemDTOList);
            // 设置批次图片
            outputComponent.setBatchImg(itemDTOList);
            bizReceiptOutputPreVO.setPreReceiptCode(entry.getKey());
            bizReceiptOutputPreVO.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue());
            bizReceiptOutputPreVO.setReceiptType(EnumReceiptType.STOCK_OUTPUT_PURCHASE_RETURN.getValue());
            bizReceiptOutputPreVO.setPreReceiptType(EnumReceiptType.PURCHASE_RECEIPT.getValue());
            bizReceiptOutputPreVO.setChildren(itemDTOList);
            // 设置创建者、创建时间
            bizReceiptOutputPreVO.setErpCreateUserName(itemDTOList.get(0).getErpCreateUserName());
            bizReceiptOutputPreVO.setErpCreateTime(itemDTOList.get(0).getErpCreateTime());
            bizReceiptOutputPreVOList.add(bizReceiptOutputPreVO);
        }
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(bizReceiptOutputPreVOList));
    }

    /**
     * 查询采购验收行项目列表
     *
     * @param ctx 上下文
     */
    public void getPurchaseInspectItemList(BizContext ctx) {
        // 入参上下文
        PreReceiptQueryPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        po.setPreReceiptType(EnumReceiptType.PURCHASE_INSPECTION.getValue());
        List<BizReceiptInspectItemDTO> purchaseInspectItemVoList = inspectFeignApi.getPurchaseInspectItemList(po);
        if (UtilCollection.isEmpty(purchaseInspectItemVoList)) {
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>());
            return;
        }
        dataFillService.fillAttr(purchaseInspectItemVoList);
        // 组装数据
        List<BizReceiptOutputPreVO> bizReceiptOutputPreVOList = new ArrayList<>();
        Map<String, List<BizReceiptInspectItemDTO>> reserveMap =
            purchaseInspectItemVoList.stream().collect(Collectors.groupingBy(BizReceiptInspectItemDTO::getReceiptCode));
        for (Map.Entry<String, List<BizReceiptInspectItemDTO>> entry : reserveMap.entrySet()) {
            BizReceiptOutputPreVO bizReceiptOutputPreVO = new BizReceiptOutputPreVO();
            List<BizReceiptOutputItemDTO> itemDTOList = new ArrayList<>();
            for (BizReceiptInspectItemDTO inspectItemDTO : entry.getValue()) {
                BizReceiptOutputItemDTO itemDTO = UtilBean.newInstance(inspectItemDTO, BizReceiptOutputItemDTO.class);
                itemDTO.setId(null);
                itemDTO.setHeadId(null);
                itemDTO.setReceiptType(EnumReceiptType.STOCK_OUTPUT_PURCHASE_RETURN.getValue());
                itemDTO.setPreReceiptHeadId(inspectItemDTO.getHeadId());
                itemDTO.setPreReceiptItemId(inspectItemDTO.getId());
                itemDTO.setPreReceiptType(EnumReceiptType.PURCHASE_INSPECTION.getValue());
                itemDTO.setPreReceiptRid(inspectItemDTO.getRid());
                itemDTO.setPreReceiptCode(entry.getKey());
                itemDTO.setPreReceiptQty(inspectItemDTO.getQty());
                itemDTO.setReferReceiptHeadId(inspectItemDTO.getHeadId());
                itemDTO.setReferReceiptItemId(inspectItemDTO.getId());
                itemDTO.setReferReceiptType(EnumReceiptType.PURCHASE_RECEIPT.getValue());
                itemDTO.setErpCreateUserName(inspectItemDTO.getCreateUserName());
                itemDTO.setErpCreateTime(inspectItemDTO.getCreateTime());
                itemDTO.setReceiptQty(inspectItemDTO.getQty());
                itemDTO.setTaskQty(BigDecimal.ZERO);
                itemDTO.setQty(BigDecimal.ZERO);
                itemDTO.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue());
                itemDTO.setMatDocCode(null);
                itemDTO.setMatDocRid(null);
                itemDTO.setMatDocYear(null);
                itemDTOList.add(itemDTO);
            }
            // 设置物料工厂信息(是否启用erp批次生产批次启用包装)
            outputComponent.setMaterialFactoryInfo(itemDTOList);
            // 设置可用数量
            this.setAvailableQty(itemDTOList);
            // 设置批次图片
            outputComponent.setBatchImg(itemDTOList);
            bizReceiptOutputPreVO.setPreReceiptCode(entry.getKey());
            bizReceiptOutputPreVO.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue());
            bizReceiptOutputPreVO.setReceiptType(EnumReceiptType.STOCK_OUTPUT_PURCHASE_RETURN.getValue());
            bizReceiptOutputPreVO.setPreReceiptType(EnumReceiptType.PURCHASE_INSPECTION.getValue());
            bizReceiptOutputPreVO.setChildren(itemDTOList);
            // 设置创建者、创建时间
            bizReceiptOutputPreVO.setErpCreateUserName(itemDTOList.get(0).getCreateUserName());
            bizReceiptOutputPreVO.setErpCreateTime(itemDTOList.get(0).getCreateTime());
            bizReceiptOutputPreVOList.add(bizReceiptOutputPreVO);
        }
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(bizReceiptOutputPreVOList));
    }

    /**
     * 查询采购入库行项目列表
     *
     * @param ctx 上下文
     */
    public void getPurchaseInputItemList(BizContext ctx) {
        // 入参上下文
        PreReceiptQueryPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        po.setPreReceiptType(EnumReceiptType.STOCK_INPUT_PURCHASE.getValue());
        List<BizReceiptInputItemDTO> purchaseInputItemVoList = inputFeignApi.getPurchaseInputItemList(po);
        if (UtilCollection.isEmpty(purchaseInputItemVoList)) {
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>());
            return;
        }
        dataFillService.fillAttr(purchaseInputItemVoList);
        // 组装数据
        List<BizReceiptOutputPreVO> bizReceiptOutputPreVOList = new ArrayList<>();
        Map<String, List<BizReceiptInputItemDTO>> reserveMap =
            purchaseInputItemVoList.stream().collect(Collectors.groupingBy(BizReceiptInputItemDTO::getReceiptCode));
        for (Map.Entry<String, List<BizReceiptInputItemDTO>> entry : reserveMap.entrySet()) {
            BizReceiptOutputPreVO bizReceiptOutputPreVO = new BizReceiptOutputPreVO();
            List<BizReceiptOutputItemDTO> itemDTOList = new ArrayList<>();
            for (BizReceiptInputItemDTO inputItemDTO : entry.getValue()) {
                BizReceiptOutputItemDTO itemDTO = UtilBean.newInstance(inputItemDTO, BizReceiptOutputItemDTO.class);
                itemDTO.setId(null);
                itemDTO.setHeadId(null);
                itemDTO.setReceiptType(EnumReceiptType.STOCK_OUTPUT_PURCHASE_RETURN.getValue());
                itemDTO.setPreReceiptHeadId(inputItemDTO.getHeadId());
                itemDTO.setPreReceiptItemId(inputItemDTO.getId());
                itemDTO.setPreReceiptType(EnumReceiptType.STOCK_INPUT_PURCHASE.getValue());
                itemDTO.setPreReceiptRid(inputItemDTO.getRid());
                itemDTO.setPreReceiptCode(entry.getKey());
                itemDTO.setPreReceiptQty(inputItemDTO.getQty());
                itemDTO.setReferReceiptHeadId(inputItemDTO.getHeadId());
                itemDTO.setReferReceiptItemId(inputItemDTO.getId());
                itemDTO.setReferReceiptType(EnumReceiptType.PURCHASE_RECEIPT.getValue());
                itemDTO.setErpCreateUserName(inputItemDTO.getCreateUserName());
                itemDTO.setErpCreateTime(inputItemDTO.getCreateTime());
                itemDTO.setReceiptQty(inputItemDTO.getQty());
                itemDTO.setTaskQty(BigDecimal.ZERO);
                itemDTO.setQty(BigDecimal.ZERO);
                itemDTO.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue());
                itemDTO.setMatDocCode(null);
                itemDTO.setMatDocRid(null);
                itemDTO.setMatDocYear(null);
                itemDTOList.add(itemDTO);
            }
            // 设置物料工厂信息(是否启用erp批次生产批次启用包装)
            outputComponent.setMaterialFactoryInfo(itemDTOList);
            // 设置可用数量
            this.setAvailableQty(itemDTOList);
            // 设置批次图片
            outputComponent.setBatchImg(itemDTOList);
            bizReceiptOutputPreVO.setPreReceiptCode(entry.getKey());
            bizReceiptOutputPreVO.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue());
            bizReceiptOutputPreVO.setReceiptType(EnumReceiptType.STOCK_OUTPUT_PURCHASE_RETURN.getValue());
            bizReceiptOutputPreVO.setPreReceiptType(EnumReceiptType.STOCK_INPUT_PURCHASE.getValue());
            bizReceiptOutputPreVO.setChildren(itemDTOList);
            // 设置创建者、创建时间
            bizReceiptOutputPreVO.setErpCreateUserName(itemDTOList.get(0).getCreateUserName());
            bizReceiptOutputPreVO.setErpCreateTime(itemDTOList.get(0).getCreateTime());
            bizReceiptOutputPreVOList.add(bizReceiptOutputPreVO);
        }
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(bizReceiptOutputPreVOList));
    }

    /**
     * 设置初始化信息
     *
     * @param ctx 上下文
     */
    public void setInit(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        ButtonVO buttonVO = new ButtonVO().setButtonSave(true).setButtonSubmit(true);
        BizResultVO<BizReceiptOutputHeadDTO> resultVO = new BizResultVO<>(
            new BizReceiptOutputHeadDTO().setReceiptType(EnumReceiptType.STOCK_OUTPUT_PURCHASE_RETURN.getValue())
                .setCreateTime(UtilDate.getNow()).setCreateUserName(user.getUserName()),
            new ExtendVO(), buttonVO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }
    

    /**
     * sap过账
     *
     * @param ctx 上下文
     */
    public void postToSap(BizContext ctx) {
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser currentUser = ctx.getCurrentUser();
        Set<Integer> statusSet = new HashSet<>();
        statusSet.add(EnumReceiptStatus.RECEIPT_STATUS_IN_TASK.getValue());
        statusSet.add(EnumReceiptStatus.RECEIPT_STATUS_TASK.getValue());
        statusSet.add(EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
        // 过滤没有sap凭证的行项目
        List<BizReceiptOutputItemDTO> syncList = headDTO.getItemDTOList().stream()
                .filter(item -> statusSet.contains(item.getItemStatus()) && !StringUtils.hasText(item.getMatDocCode())).collect(Collectors.toList());
        if (UtilCollection.isEmpty(syncList)) {
            return;
        }
        headDTO.setItemDTOList(syncList);
        // 过滤后重新设置上下文-headDTO
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
        /* ******** 设置过账账期 ******** */
        this.setInPostDate(headDTO, currentUser);

        HXPostingHeader header = this.materialOutputPosting(headDTO, syncList, false);
        HXPostingReturn returnObj = hxInterfaceService.posting(header,false);
        if (Const.ERP_RETURN_TYPE_S.equalsIgnoreCase(returnObj.getSuccess())) {

            // 更新过账时间和过账标识
            this.updatePostingDateAndIsPost(ctx);
            // 修改出库单SAP物料凭证字段
            this.updateItemAfterSyncSap(syncList, returnObj);
            // 更新单据行项目状态已记账
            outputComponent.updateStatus(headDTO, syncList, EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue());
            List<Long> itemIdList = syncList.stream().map(BizReceiptOutputItemDTO::getId).collect(Collectors.toList());
            log.debug("出库单{}行项目{}过账同步SAP成功", headDTO.getReceiptCode(), itemIdList.toString());
        } else {
            log.warn("出库单{}过账同步SAP失败", headDTO.getReceiptCode());
            // 失败时，更新出库单及行项目为【未同步】状态
            if (!checkCompletedStatus(headDTO)) {
                outputComponent.updateStatus(headDTO, syncList, EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
            }
            throw new WmsException(EnumReturnMsg.RETURN_CODE_INTERFACE_CALL_FAILURE, returnObj.getReturnMessage());
        }
    }

    public boolean checkCompletedStatus(BizReceiptOutputHeadDTO headDTO) {
        return bizReceiptOutputHeadDataWrap.getById(headDTO.getId()).getReceiptStatus().equals(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
    }

    /**
     * 更新过账时间和过账标识
     *
     * @param ctx 上下文
     */
    public void updatePostingDateAndIsPost(BizContext ctx) {
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - ins凭证
        StockInsMoveTypePostTaskDTO insMoveTypeDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_INS);
        CurrentUser user = ctx.getCurrentUser();
        List<BizReceiptOutputItemDTO> itemDTOList = headDTO.getItemDTOList();
        Date postingDate = itemDTOList.get(0).getPostingDate();
        if (UtilObject.isNull(postingDate)) {
            postingDate = UtilLocalDateTime.getDate(LocalDateTime.now());
        }
        // 判断过账日期是否在帐期内
        postingDate = bizCommonService.checkAndUpdateInPostDate(postingDate, user.getId());
        Date now = UtilDate.getNow();
        for (BizReceiptOutputItemDTO outputItemDTO : itemDTOList) {
            outputItemDTO.setDocDate(now);
            outputItemDTO.setPostingDate(postingDate);
            outputItemDTO.setIsPost(EnumRealYn.TRUE.getIntValue());
        }
        // 补全凭证的过账日期、凭证日期
        if (UtilObject.isNotNull(insMoveTypeDTO)) {
            for (StockInsDocBatch stockInsDocBatch : insMoveTypeDTO.getPostDTO().getInsDocBatchList()) {
                stockInsDocBatch.setDocDate(now);
                stockInsDocBatch.setPostingDate(postingDate);
            }
        }
        bizReceiptOutputItemDataWrap.updateBatchDtoById(itemDTOList);
        // 上下文返回参数- ins凭证
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_INS, insMoveTypeDTO);
    }

    /**
     * 过账成功，修改出库单SAP物料凭证字段,年,凭证行号,凭证时间
     *
     * @param itemDTOList 行项目列表
     * @param returnObj      sap返回结果
     */
    public void updateItemAfterSyncSap(List<BizReceiptOutputItemDTO> itemDTOList, HXPostingReturn returnObj) {
        for (BizReceiptOutputItemDTO itemDTO : itemDTOList) {
            itemDTO.setMatDocCode(returnObj.getMatDocCode());
            itemDTO.setMatDocRid(itemDTO.getRid());
            itemDTO.setMatDocYear(UtilObject.getStringOrEmpty(returnObj.getMatDocYear()));
        }
        bizReceiptOutputItemDataWrap.updateBatchDtoById(itemDTOList);
    }

    /**
     * 过账前设置行项目账期
     *
     * @param headDTO 单据行项目
     * @param user        当前用户
     */
    private void setInPostDate(BizReceiptOutputHeadDTO headDTO, CurrentUser user) {
        if (UtilObject.isEmpty(headDTO)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_ACCOUNT_SET_FAIL);
        }
        List<BizReceiptOutputItemDTO> itemDTOList = headDTO.getItemDTOList();
        Date postingDate = headDTO.getPostingDate();
        Date writeOffPostingDate = itemDTOList.get(0).getWriteOffPostingDate();
        if (UtilObject.isNull(postingDate)) {
            postingDate = UtilLocalDateTime.getDate(LocalDateTime.now());
        }
        if (UtilObject.isNull(writeOffPostingDate)) {
            writeOffPostingDate = UtilLocalDateTime.getDate(LocalDateTime.now());
        }
        // 判断过账日期是否在帐期内
        postingDate = bizCommonService.checkAndUpdateInPostDate(postingDate, user.getId());
        writeOffPostingDate = bizCommonService.checkAndUpdateInPostDate(writeOffPostingDate, user.getId());
        for (BizReceiptOutputItemDTO inspectItemDTO : itemDTOList) {
            if (EnumRealYn.FALSE.getIntValue().equals(inspectItemDTO.getIsWriteOff())) {
                inspectItemDTO.setDocDate(UtilDate.getNow());
                inspectItemDTO.setPostingDate(postingDate);
            } else {
                inspectItemDTO.setWriteOffDocDate(UtilDate.getNow());
                inspectItemDTO.setWriteOffPostingDate(writeOffPostingDate);
            }
        }
    }

     /**
     * 领料出库单过账或者冲销入参
     */
    private HXPostingHeader materialOutputPosting(BizReceiptOutputHeadDTO headDTO, List<BizReceiptOutputItemDTO> itemList, boolean isWriteOff) {

        HXPostingHeader header = new HXPostingHeader();
        header.setReceiptId(headDTO.getId());
        header.setReceiptCode(headDTO.getReceiptCode());
        header.setReceiptType(headDTO.getReceiptType());
        if (isWriteOff) {
            header.setPostingDate(UtilDate.convertDateToDateStr(itemList.get(0).getWriteOffPostingDate()));
            header.setDocDate(UtilDate.convertDateToDateStr(itemList.get(0).getWriteOffDocDate()));
        } else {
            header.setPostingDate(UtilDate.convertDateToDateStr(itemList.get(0).getPostingDate()));
            header.setDocDate(UtilDate.convertDateToDateStr(itemList.get(0).getDocDate()));
        }

        List<HXPostingItem> items = itemList.stream().map(output -> {
            HXPostingItem item = new HXPostingItem();
            item.setReceiptRid(output.getRid());
            item.setFtyCode(output.getFtyCode());
            item.setLocationCode1(output.getLocationCode());
            item.setMatCode(output.getMatCode());
            item.setQty(UtilBigDecimal.getString(output.getQty()));
            item.setUnitCode(output.getUnitCode());

            if (isWriteOff) {
                item.setMoveType(EnumMoveType.MOVE_TYPE_102.getValue());
            }else {
                item.setMoveType(EnumMoveType.MOVE_TYPE_101.getValue());
            }
            item.setPurchaseOrderCode(output.getReturnPurchaseCode());
            item.setPurchaseOrderItemCode(output.getReturnPurchaseRid());

            return item;
        }).collect(Collectors.toList());

        header.setItems(items);

        return header;
    }


    /**
     * sap冲销
     *
     * @param ctx 上下文
     */
    public void writeOffToSap(BizContext ctx) {
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        List<BizReceiptOutputItemDTO> itemDTOList = headDTO.getItemDTOList();
        itemDTOList.forEach(x -> x.setIsWriteOff(1));
        itemDTOList.forEach(item->{
            // 设置冲销标识。
            item.setIsWriteOff(1);
        });
        this.setInPostDate(headDTO,ctx.getCurrentUser());

        HXPostingHeader header = this.materialOutputPosting(headDTO, itemDTOList, true);

        HXPostingReturn returnObj = hxInterfaceService.posting(header,true);

        if (Const.ERP_RETURN_TYPE_S.equalsIgnoreCase(returnObj.getSuccess())) {
            // 更新单据行项目状态冲销
            outputComponent.updateStatus(headDTO, itemDTOList, EnumReceiptStatus.RECEIPT_STATUS_WRITED_OFF.getValue());
            List<Long> itemIdList = itemDTOList.stream().map(BizReceiptOutputItemDTO::getId).collect(Collectors.toList());

            // 更新冲销过账时间和冲销标识
            this.updateWriteOffPostingDateAndIsWriteOff(ctx);
            // 冲销成功，修改出库单SAP物料凭证字段
            // 调用SAP
            
            this.updateItemAfterWriteOffSap(itemDTOList, returnObj);
            
            log.debug("出库单{}行项目{}冲销同步SAP成功", headDTO.getReceiptCode(), itemIdList.toString());

        } else {
            log.warn("出库单{}冲销同步SAP失败，返回信息：{}", headDTO.getId(), returnObj.getReturnMessage());
            throw new WmsException(EnumReturnMsg.RETURN_CODE_INTERFACE_CALL_FAILURE, returnObj.getReturnMessage());
        }
    }

    /**
     * 更新冲销过账时间
     *
     * @param ctx 上下文
     */
    public void updateWriteOffPostingDateAndIsWriteOff(BizContext ctx) {
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 入参上下文 - ins凭证
        StockInsMoveTypePostTaskDTO insMoveTypeDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_INS);
        CurrentUser user = ctx.getCurrentUser();
        List<BizReceiptOutputItemDTO> itemDTOList = headDTO.getItemDTOList();
        // 校验账期
        Date postingDate = itemDTOList.get(0).getPostingDate();
        if (UtilObject.isNull(postingDate)) {
            postingDate = UtilLocalDateTime.getDate(LocalDateTime.now());
        }
        // 判断过账日期是否在帐期内
        postingDate = bizCommonService.checkAndUpdateInPostDate(postingDate, user.getId());
        Date now = UtilDate.getNow();
        for (BizReceiptOutputItemDTO outputItemDTO : itemDTOList) {
            outputItemDTO.setWriteOffDocDate(now);
            outputItemDTO.setWriteOffPostingDate(postingDate);
            outputItemDTO.setIsWriteOff(EnumRealYn.TRUE.getIntValue());
        }
        // 补全凭证的冲销过账日期、冲销凭证日期
        if (UtilObject.isNotNull(insMoveTypeDTO)) {
            for (StockInsDocBatch stockInsDocBatch : insMoveTypeDTO.getPostDTO().getInsDocBatchList()) {
                stockInsDocBatch.setDocDate(now);
                stockInsDocBatch.setPostingDate(postingDate);
            }
        }
        bizReceiptOutputItemDataWrap.updateBatchDtoById(itemDTOList);
        // 上下文返回参数- ins凭证
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_INS, insMoveTypeDTO);
    }

    /**
     * 冲销成功，修改出库单SAP物料冲销凭证字段,年,冲销凭证行号
     *
     * @param itemDTOList 行项目列表
     * @param returnObj      sap返回结果
     */
    public void updateItemAfterWriteOffSap(List<BizReceiptOutputItemDTO> itemDTOList, HXPostingReturn returnObj) {
        
        for (BizReceiptOutputItemDTO itemDTO : itemDTOList) {
            itemDTO.setWriteOffMatDocCode(returnObj.getMatDocCode());
            itemDTO.setWriteOffMatDocRid(UtilObject.getStringOrEmpty(itemDTO.getRid()));
            itemDTO.setWriteOffMatDocYear(UtilObject.getStringOrEmpty(returnObj.getMatDocYear()));
        }
        bizReceiptOutputItemDataWrap.updateBatchDtoById(itemDTOList);
        
    }

}
