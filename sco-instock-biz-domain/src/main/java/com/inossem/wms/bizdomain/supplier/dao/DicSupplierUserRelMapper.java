package com.inossem.wms.bizdomain.supplier.dao;

import com.inossem.wms.common.model.auth.user.entity.SysUser;
import com.inossem.wms.common.model.masterdata.supplier.entity.DicSupplier;
import com.inossem.wms.common.model.masterdata.supplier.entity.DicSupplierUserRel;
import com.inossem.wms.common.mybatisplus.WmsBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 供应商主数据 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
public interface DicSupplierUserRelMapper extends WmsBaseMapper<DicSupplierUserRel> {

    /**
     * 根据供应商id查询关联的用户列表
     *
     * @param supplierId 供应商id
     * @return 用户列表
     */
    List<SysUser> selectUserListBySupplierId(@Param("supplierId") Long supplierId);

}
