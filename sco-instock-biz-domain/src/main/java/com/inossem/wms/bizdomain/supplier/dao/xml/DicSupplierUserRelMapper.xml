<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizdomain.supplier.dao.DicSupplierUserRelMapper">

    <!-- 根据供应商ID查询关联的用户列表 -->
    <select id="selectUserListBySupplierId" resultType="com.inossem.wms.common.model.auth.user.entity.SysUser">
        SELECT u.*
        FROM dic_supplier_user_rel rel
        JOIN dic_supplier ds ON rel.supplier_id = ds.id AND ds.is_delete = 0
        LEFT JOIN sys_user u ON rel.user_id = u.id AND u.is_delete = 0
        WHERE ds.id = #{supplierId}
          AND rel.is_delete = 0
    </select>

</mapper>
