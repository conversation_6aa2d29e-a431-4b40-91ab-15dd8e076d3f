package com.inossem.wms.bizdomain.supplier.service.datawrap;

import com.inossem.wms.bizdomain.supplier.dao.DicSupplierUserRelMapper;
import com.inossem.wms.common.model.auth.user.entity.SysUser;
import com.inossem.wms.common.model.masterdata.supplier.entity.DicSupplierUserRel;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import com.inossem.wms.common.util.UtilNumber;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 供应商主数据 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
@Service
public class DicSupplierUserRelDataWrap extends BaseDataWrap<DicSupplierUserRelMapper, DicSupplierUserRel> {

    /**
     * 根据供应商id查询关联的用户列表
     *
     * @param supplierId 供应商id
     * @return 用户列表
     */
    public List<SysUser> selectUserListBySupplierId(Long supplierId) {
        if (UtilNumber.isEmpty(supplierId)) {
            return new ArrayList<>();
        }
        return this.baseMapper.selectUserListBySupplierId(supplierId);
    }

}
