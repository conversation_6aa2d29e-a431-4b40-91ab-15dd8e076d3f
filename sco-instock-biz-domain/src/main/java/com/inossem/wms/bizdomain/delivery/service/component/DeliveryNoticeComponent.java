package com.inossem.wms.bizdomain.delivery.service.component;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.common.service.biz.*;
import com.inossem.wms.bizbasis.erp.service.biz.PurchaseReceiptService;
import com.inossem.wms.bizbasis.erp.service.datawrap.ErpPurchaseReceiptHeadDataWrap;
import com.inossem.wms.bizbasis.erp.service.datawrap.ErpPurchaseReceiptItemDataWrap;
import com.inossem.wms.bizbasis.masterdata.org.service.datawrap.DicDeptDataWrap;
import com.inossem.wms.bizbasis.masterdata.org.service.datawrap.DicStockLocationDataWrap;
import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDeptOfficeRelDataWrap;
import com.inossem.wms.bizbasis.sap.restful.service.HXSapIntegerfaceService;
import com.inossem.wms.bizdomain.contract.service.component.ContractComponent;
import com.inossem.wms.bizdomain.contract.service.datawrap.BizReceiptContractHeadDataWrap;
import com.inossem.wms.bizdomain.contract.service.datawrap.BizReceiptContractItemDataWrap;
import com.inossem.wms.bizdomain.delivery.service.datawrap.BizReceiptDeliveryNoticeCaseRelDataWrap;
import com.inossem.wms.bizdomain.delivery.service.datawrap.BizReceiptDeliveryNoticeD2dItemDataWrap;
import com.inossem.wms.bizdomain.delivery.service.datawrap.BizReceiptDeliveryNoticeHeadDataWrap;
import com.inossem.wms.bizdomain.delivery.service.datawrap.BizReceiptDeliveryNoticeItemDataWrap;
import com.inossem.wms.bizdomain.demandplan.service.datawrap.BizReceiptDemandPlanHeadDataWrap;
import com.inossem.wms.bizdomain.input.service.datawrap.BizReceiptInputItemDataWrap;
import com.inossem.wms.bizdomain.logistics.service.biz.LogisticsService;
import com.inossem.wms.bizdomain.logistics.service.component.LogisticsComponent;
import com.inossem.wms.bizdomain.logistics.service.datawrap.BizReceiptLogisticsHeadDataWrap;
import com.inossem.wms.bizdomain.logistics.service.datawrap.BizReceiptLogisticsItemDataWrap;
import com.inossem.wms.bizdomain.register.service.datawrap.BizReceiptRegisterHeadDataWrap;
import com.inossem.wms.bizdomain.register.service.datawrap.BizReceiptRegisterItemDataWrap;
import com.inossem.wms.bizdomain.settlement.service.datawrap.BizReceiptPaymentPlanHeadDataWrap;
import com.inossem.wms.bizdomain.settlement.service.datawrap.BizReceiptPaymentPlanItemDataWrap;
import com.inossem.wms.bizdomain.supplier.service.datawrap.DicSupplierDataWrap;
import com.inossem.wms.bizdomain.supplier.service.datawrap.DicSupplierUserRelDataWrap;
import com.inossem.wms.bizdomain.suppliercase.service.component.SupplierCaseComponent;
import com.inossem.wms.bizdomain.suppliercase.service.datawrap.BizReceiptSupplierCaseHeadDataWrap;
import com.inossem.wms.bizdomain.suppliercase.service.datawrap.BizReceiptSupplierCaseItemDataWrap;
import com.inossem.wms.bizdomain.suppliercase.service.datawrap.BizReceiptSupplierCaseRelDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.*;
import com.inossem.wms.common.enums.auth.EnumUserJob;
import com.inossem.wms.common.enums.contract.EnumContractCurrency;
import com.inossem.wms.common.enums.contract.EnumContractPaymentNode;
import com.inossem.wms.common.enums.contract.EnumContractTaxRate;
import com.inossem.wms.common.enums.dept.EnumDept;
import com.inossem.wms.common.enums.purchase.EnumPurchaseType;
import com.inossem.wms.common.enums.sap.EnumPurchaseOrderType;
import com.inossem.wms.common.enums.workflow.EnumApprovalLevel;
import com.inossem.wms.common.enums.workflow.EnumApprovalStatus;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.approval.dto.BizApprovalReceiptInstanceRelDTO;
import com.inossem.wms.common.model.approval.dto.BizApproveRecordDTO;
import com.inossem.wms.common.model.approval.dto.RevokeDTO;
import com.inossem.wms.common.model.auth.rel.entity.SysUserRoleRel;
import com.inossem.wms.common.model.auth.user.entity.SysUser;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptRelation;
import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptApplyHeadDTO;
import com.inossem.wms.common.model.bizdomain.contract.dto.BizReceiptContractHeadDTO;
import com.inossem.wms.common.model.bizdomain.contract.dto.BizReceiptContractItemDTO;
import com.inossem.wms.common.model.bizdomain.contract.dto.BizReceiptContractItemQtyDTO;
import com.inossem.wms.common.model.bizdomain.contract.dto.BizReceiptContractPaymentNodeDTO;
import com.inossem.wms.common.model.bizdomain.contract.entity.BizReceiptContractHead;
import com.inossem.wms.common.model.bizdomain.contract.entity.BizReceiptContractItem;
import com.inossem.wms.common.model.bizdomain.contract.po.BizReceiptContractSearchPO;
import com.inossem.wms.common.model.bizdomain.delivery.dto.BizReceiptDeliveryNoticeCaseRelDTO;
import com.inossem.wms.common.model.bizdomain.delivery.dto.BizReceiptDeliveryNoticeD2dItemDTO;
import com.inossem.wms.common.model.bizdomain.delivery.dto.BizReceiptDeliveryNoticeHeadDTO;
import com.inossem.wms.common.model.bizdomain.delivery.dto.BizReceiptDeliveryNoticeItemDTO;
import com.inossem.wms.common.model.bizdomain.delivery.entity.BizReceiptDeliveryNoticeCaseRel;
import com.inossem.wms.common.model.bizdomain.delivery.entity.BizReceiptDeliveryNoticeD2dItem;
import com.inossem.wms.common.model.bizdomain.delivery.entity.BizReceiptDeliveryNoticeHead;
import com.inossem.wms.common.model.bizdomain.delivery.entity.BizReceiptDeliveryNoticeItem;
import com.inossem.wms.common.model.bizdomain.delivery.po.*;
import com.inossem.wms.common.model.bizdomain.delivery.vo.BizReceiptDeliveryNoticeListVo;
import com.inossem.wms.common.model.bizdomain.delivery.vo.BizReceiptDeliveryNoticePreHeadVo;
import com.inossem.wms.common.model.bizdomain.demandplan.entity.BizReceiptDemandPlanHead;
import com.inossem.wms.common.model.bizdomain.input.entity.BizReceiptInputItem;
import com.inossem.wms.common.model.bizdomain.logistics.dto.BizReceiptLogisticsCaseRelDTO;
import com.inossem.wms.common.model.bizdomain.logistics.dto.BizReceiptLogisticsHeadDTO;
import com.inossem.wms.common.model.bizdomain.logistics.dto.BizReceiptLogisticsItemDTO;
import com.inossem.wms.common.model.bizdomain.logistics.entity.BizReceiptLogisticsHead;
import com.inossem.wms.common.model.bizdomain.logistics.entity.BizReceiptLogisticsItem;
import com.inossem.wms.common.model.bizdomain.register.dto.BizReceiptRegisterHeadDTO;
import com.inossem.wms.common.model.bizdomain.register.dto.BizReceiptRegisterItemDTO;
import com.inossem.wms.common.model.bizdomain.register.entity.BizReceiptRegisterHead;
import com.inossem.wms.common.model.bizdomain.register.entity.BizReceiptRegisterItem;
import com.inossem.wms.common.model.bizdomain.settlement.dto.BizReceiptPaymentPlanHeadDTO;
import com.inossem.wms.common.model.bizdomain.settlement.dto.BizReceiptPaymentPlanItemDTO;
import com.inossem.wms.common.model.bizdomain.settlement.entity.BizReceiptPaymentPlanHead;
import com.inossem.wms.common.model.bizdomain.settlement.entity.BizReceiptPaymentPlanItem;
import com.inossem.wms.common.model.bizdomain.suppliercase.dto.BizReceiptSupplierCaseHeadDTO;
import com.inossem.wms.common.model.bizdomain.suppliercase.dto.BizReceiptSupplierCaseItemDTO;
import com.inossem.wms.common.model.bizdomain.suppliercase.dto.BizReceiptSupplierCaseRelDTO;
import com.inossem.wms.common.model.bizdomain.suppliercase.entity.BizReceiptSupplierCaseItem;
import com.inossem.wms.common.model.bizdomain.suppliercase.entity.BizReceiptSupplierCaseRel;
import com.inossem.wms.common.model.bizdomain.suppliercase.po.BizReceiptSupplierCaseSearchPO;
import com.inossem.wms.common.model.bizdomain.suppliercase.vo.BizReceiptSupplierCaseListVo;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.*;
import com.inossem.wms.common.model.erp.dto.ErpPurchaseReceiptItemDTO;
import com.inossem.wms.common.model.erp.entity.ErpPurchaseReceiptHead;
import com.inossem.wms.common.model.erp.entity.ErpPurchaseReceiptItem;
import com.inossem.wms.common.model.erp.po.BizReceiptPreSearchPO;
import com.inossem.wms.common.model.masterdata.base.entity.DicDept;
import com.inossem.wms.common.model.masterdata.mat.info.entity.DicMaterial;
import com.inossem.wms.common.model.masterdata.supplier.dto.DicSupplierDTO;
import com.inossem.wms.common.model.masterdata.supplier.entity.DicSupplier;
import com.inossem.wms.common.model.masterdata.supplier.entity.DicSupplierUserRel;
import com.inossem.wms.common.model.masterdata.supplier.po.DicSupplierPO;
import com.inossem.wms.common.model.org.location.dto.DicStockLocationDTO;
import com.inossem.wms.common.model.org.location.entity.DicStockLocation;
import com.inossem.wms.common.model.sap.purchase.*;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import com.inossem.wms.common.util.*;
import com.inossem.wms.common.util.excel.UtilExcel;
import com.inossem.wms.system.workflow.service.business.biz.ApprovalService;
import com.inossem.wms.system.workflow.service.business.biz.WorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <p>
 * 送货通知组件库
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-05-17
 */

@Service
@Slf4j
public class DeliveryNoticeComponent {

    @Autowired
    protected DataFillService dataFillService;

    @Autowired
    private BizReceiptDeliveryNoticeHeadDataWrap deliveryNoticeHeadDataWrap;

    @Autowired
    private BizReceiptDeliveryNoticeItemDataWrap deliveryNoticeItemDataWrap;

    @Autowired
    private BizReceiptDeliveryNoticeD2dItemDataWrap deliveryNoticeD2dItemDataWrap;

    @Autowired
    protected PurchaseReceiptService purchaseReceiptService;

    @Autowired
    protected DictionaryService dictionaryService;
    @Autowired
    private DicDeptDataWrap dicDeptDataWrap;
    @Autowired
    protected ReceiptOperationLogService receiptOperationLogService;

    @Autowired
    protected ReceiptAttachmentService receiptAttachmentService;

    @Autowired
    private ReceiptRelationService receiptRelationService;

    @Autowired
    private BizCommonService bizCommonService;

    @Autowired
    protected DicStockLocationDataWrap dicStockLocationDataWrap;

    @Autowired
    protected BizReceiptDeliveryNoticeCaseRelDataWrap noticeCaseRelDataWrap;


    @Autowired
    protected WorkflowService workflowService;

    @Autowired
    protected ApprovalService approvalService;

    @Autowired
    private BizReceiptContractHeadDataWrap bizReceiptContractHeadDataWrap;

    @Autowired
    private BizReceiptContractItemDataWrap bizReceiptContractItemDataWrap;

    @Autowired
    private ContractComponent contractComponent;

    @Autowired
    private BizReceiptSupplierCaseHeadDataWrap supplierCaseHeadDataWrap;

    @Autowired
    private SupplierCaseComponent supplierCaseComponent;
    
    @Autowired
    private BizReceiptSupplierCaseItemDataWrap supplierCaseItemDataWrap;

    @Autowired
    private BizReceiptSupplierCaseRelDataWrap supplierCaseRelDataWrap;
    @Autowired
    private LogisticsComponent logisticsComponent;

    @Autowired
    private BizReceiptLogisticsHeadDataWrap logisticsHeadDataWrap;

    @Autowired
    private BizReceiptLogisticsItemDataWrap logisticsItemDataWrap;

    @Autowired
    private SysUserDeptOfficeRelDataWrap sysUserDeptOfficeRelDataWrap;

    @Autowired
    private DicSupplierDataWrap dicSupplierDataWrap;

    @Autowired
    private LogisticsService logisticsService;

    @Autowired
    private BizReceiptInputItemDataWrap inputItemDataWrap;  

    @Autowired
    private ErpPurchaseReceiptHeadDataWrap erpPurchaseReceiptHeadDataWrap;
    @Autowired
    private ErpPurchaseReceiptItemDataWrap erpPurchaseReceiptItemDataWrap;

    @Autowired
    private DicSupplierUserRelDataWrap bidderUserRelDataWrap;
    @Autowired
    private BizReceiptDemandPlanHeadDataWrap bizReceiptDemandPlanHeadDataWrap;

    @Autowired
    private BizReceiptPaymentPlanHeadDataWrap bizReceiptPaymentPlanHeadDataWrap;

    @Autowired
    private BizReceiptPaymentPlanItemDataWrap bizReceiptPaymentPlanItemDataWrap;
    @Autowired
    private BizReceiptSupplierCaseRelDataWrap bizReceiptSupplierCaseRelDataWrap;


    /**
     * 查询库存地点-根据选中的工厂id获取
     *
     * @out ctx 出参 {@link MultiResultVO <> ("dicCarTypeDataWrap.list()":"库存地点下拉框")}
     */
    public void getLocationList(BizContext ctx) {
        // 从上下文获取工厂id
        Long ftyId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        // 查询库存地点下拉
        QueryWrapper<DicStockLocation> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DicStockLocation::getFtyId, ftyId);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(dicStockLocationDataWrap.list(queryWrapper)));
    }

    /**
     * 页面初始化: 1、设置送货通知【单据类型、创建时间、创建人】 2、设置按钮权限【提交、保存】
     *
     * @in ctx 入参
     * @out ctx 出参 {@link BizResultVO (head":"送货通知","extend":"扩展功能","button":"按钮组")}
     */
    public void setInit(BizContext ctx) {
        // 页面初始化设置
        BizResultVO<BizReceiptDeliveryNoticeHeadDTO> resultVO = new BizResultVO<>(
            new BizReceiptDeliveryNoticeHeadDTO().setReceiptType(EnumReceiptType.DELIVERY_NOTICE.getValue())
                .setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue())
                .setCreateTime(UtilDate.getNow()).setCreateUserName(ctx.getCurrentUser().getUserName())
                    .setCreateUserDeptName(ctx.getCurrentUser().getUserDeptList().get(0).getDeptName()),
                new ExtendVO(), new ButtonVO().setButtonSave(true).setButtonSubmit(true).setButtonDeliveryNoticeApprove(this.checkUserFactory(ctx.getCurrentUser())));
        // 设置页面初始化数据到上下文


        // 设置税码枚举列表
        resultVO.getHead().setTaxRateList(EnumContractTaxRate.toList());

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 开启附件
     *
     * @in ctx 入参 {@link BizResultVO ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO ("extend":"扩展功能开启附件")}
     */
    public void setExtendAttachment(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptDeliveryNoticeHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 设置附件开启/关闭
        resultVO.getExtend().setAttachmentRequired(UtilConst.getInstance().isAttachmentRequired());
    }

    /**
     * 开启操作日志
     *
     * @in ctx 入参 {@link BizResultVO ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO ("extend":"扩展功能开启操作日志")}
     */
    public void setExtendOperationLog(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptDeliveryNoticeHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 设置操作日志开启/关闭
        resultVO.getExtend().setOperationLogRequired(UtilConst.getInstance().isOperationLogRequired());
    }

    /**
     * 开启操单据流
     *
     * @in ctx 入参 {@link BizResultVO ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO ("extend":"扩展功能开启单据流")}
     */
    public void setExtendRelation(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptDeliveryNoticeHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        resultVO.getExtend().setRelationRequired(true);
        if (UtilObject.isNotNull(resultVO.getHead())) {
            resultVO.getHead().setRelationList(receiptRelationService
                .getReceiptTree(resultVO.getHead().getReceiptType(), resultVO.getHead().getId(), null));
        }
        // 设置单据流详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 送货通知单-分页
     *
     * @in ctx 入参 {@link BizReceiptDeliveryNoticeSearchPO :"查询条件对象"}
     * @out ctx 出参 {@link PageObjectVO <BizReceiptDeliveryNoticeHeadDTO> ("dtoList":"列表数据","total":"总条数")}
     */
    public void getPage(BizContext ctx) {
        // 上下文入参
        BizReceiptDeliveryNoticeSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser user = ctx.getCurrentUser();
        // 组装查询条件
        WmsQueryWrapper<BizReceiptDeliveryNoticeSearchPO> wrapper = this.setQueryWrapper(po,user);
        // 分页处理
        IPage<BizReceiptDeliveryNoticeListVo> page = po.getPageObj(BizReceiptDeliveryNoticeListVo.class);
        // 送货通知单-分页
        deliveryNoticeHeadDataWrap.getDeliveryNoticePageVo(page, wrapper);
        // 转dto
        List<BizReceiptDeliveryNoticeHeadDTO> dtoList =
            UtilCollection.toList(page.getRecords(), BizReceiptDeliveryNoticeHeadDTO.class);
        // 填充关联属性
        dataFillService.fillRlatAttrDataList(dtoList);
        // 设置送货通知分页信息到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(dtoList, page.getTotal()));
    }

    /**
     * 送货通知单-详情
     *
     * @in ctx 入参 {"id":"主表id"}
     * @out ctx 出参 {@link BizResultVO ("head":"送货通知单详情","button":"按钮组")}
     */
    public void getInfo(BizContext ctx) {
        // 入参上下文
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取送货通知单
        BizReceiptDeliveryNoticeHead deliveryNoticeHead = deliveryNoticeHeadDataWrap.getById(headId);
        // 转DTO
        BizReceiptDeliveryNoticeHeadDTO deliveryNoticeHeadDTO =
            UtilBean.newInstance(deliveryNoticeHead, BizReceiptDeliveryNoticeHeadDTO.class);
        // 填充关联属性和父子属性
        dataFillService.fillAttr(deliveryNoticeHeadDTO);
        // 送货通知填充固定资产物料描述属性

        //填充需求计划类型
        if(UtilCollection.isNotEmpty(deliveryNoticeHeadDTO.getItemList())){
            List<String> demandPlanCodeList = deliveryNoticeHeadDTO.getItemList().stream()
                    .map(BizReceiptDeliveryNoticeItemDTO::getDemandPlanCode)
                    .filter(UtilString::isNotNullOrEmpty)
                    .collect(Collectors.toList());
            if(UtilCollection.isNotEmpty(demandPlanCodeList)) {
                QueryWrapper<BizReceiptDemandPlanHead> demandPlanHeadQueryWrapper = new QueryWrapper<>();
                demandPlanHeadQueryWrapper.lambda().in(BizReceiptDemandPlanHead::getReceiptCode, demandPlanCodeList);
                List<BizReceiptDemandPlanHead> demandPlanList = bizReceiptDemandPlanHeadDataWrap.list(demandPlanHeadQueryWrapper);
                Map<String, Integer> demandCodeRelTypeMap = demandPlanList.stream().collect(Collectors.toMap(BizReceiptDemandPlanHead::getReceiptCode, BizReceiptDemandPlanHead::getDemandType));

                for (BizReceiptDeliveryNoticeItemDTO itemDTO : deliveryNoticeHeadDTO.getItemList()) {
                    if (UtilString.isNotNullOrEmpty(itemDTO.getDemandPlanCode())) {
                        itemDTO.setDemandType(demandCodeRelTypeMap.get(itemDTO.getDemandPlanCode()));
                    }
                }
            }

        }

        // 设置按钮组权限
        ButtonVO buttonVO = this.setButton(deliveryNoticeHeadDTO, ctx.getCurrentUser());

        // 设置税码枚举列表
        deliveryNoticeHeadDTO.setTaxRateList(EnumContractTaxRate.toList());

        // 设置送货通知单详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO,
            new BizResultVO<>(deliveryNoticeHeadDTO, new ExtendVO(), buttonVO));
    }


    /**
     * 刷新合同已发货数量
     */
    public void refreshContractQty(BizReceiptDeliveryNoticeHeadDTO headDTO){
        // 刷新合同可送货数量
        if (headDTO.getItemList().get(0).getPreReceiptType().equals(EnumReceiptType.CONTRACT_RECEIPT.getValue())) {

            List<Long> idList = headDTO.getItemList().stream().map(e -> e.getPreReceiptItemId()).collect(Collectors.toList());

            List<BizReceiptContractItem> contractItemList = bizReceiptContractItemDataWrap.listByIds(idList);
            BigDecimal sum = BigDecimal.ZERO;
            BizReceiptContractHead contractHead = bizReceiptContractHeadDataWrap.getById(contractItemList.get(0).getHeadId());
            Map<Long, BizReceiptContractItem> contractItemMap = contractItemList.stream().collect(Collectors.toMap(e -> e.getId(), e -> e, (k1, k2) -> k2));
            for (BizReceiptDeliveryNoticeItemDTO itemDTO : headDTO.getItemList()) {
                BizReceiptContractItem contractItem = contractItemMap.get(itemDTO.getPreReceiptItemId());
                if(UtilObject.isNotNull(contractItem)){
                    itemDTO.setPreReceiptQty(contractItem.getQty());
                    itemDTO.setCanDeliveryQty(contractItem.getQty().subtract(contractItem.getSendQty()));
                }else{
                    itemDTO.setPreReceiptQty(BigDecimal.ZERO);
                    itemDTO.setCanDeliveryQty(BigDecimal.ZERO);
                }
                sum = sum.add(contractItem.getSendQty());
            }
            headDTO.setCanDeliveryQty(contractHead.getDemandQty().subtract(sum));

        }

        
    }

    /**
     * 封装打印数据
     * @param ctx
     */
    public void arrangePrintInfo(BizContext ctx) {
        // 入参上下文
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取送货通知单
        BizReceiptDeliveryNoticeHead deliveryNoticeHead = deliveryNoticeHeadDataWrap.getById(headId);
        // 转DTO
        BizReceiptDeliveryNoticeHeadDTO deliveryNoticeHeadDTO =
                UtilBean.newInstance(deliveryNoticeHead, BizReceiptDeliveryNoticeHeadDTO.class);
        // 填充关联属性和父子属性
        dataFillService.fillAttr(deliveryNoticeHeadDTO);
        // 属性填充
        deliveryNoticeHeadDTO.setPurchaseUserCode(deliveryNoticeHeadDTO.getItemList().get(0).getPurchaseUserCode())
                .setPurchaseUserName(deliveryNoticeHeadDTO.getItemList().get(0).getPurchaseUserName())
                .setContractCode(deliveryNoticeHeadDTO.getItemList().get(0).getContractCode())
                .setContractName(deliveryNoticeHeadDTO.getItemList().get(0).getContractName())
                .setSupplierCode(deliveryNoticeHeadDTO.getItemList().get(0).getSupplierCode())
                .setSupplierName(deliveryNoticeHeadDTO.getItemList().get(0).getSupplierName())
                .setPurchaseManagerCode(deliveryNoticeHeadDTO.getCreateUserCode())
                .setPurchaseManagerName(deliveryNoticeHeadDTO.getCreateUserName())
                .setApplyUserCode(deliveryNoticeHeadDTO.getItemList().get(0).getApplyUserCode())
                .setApplyUserName(deliveryNoticeHeadDTO.getItemList().get(0).getApplyUserName());
        // 封装打印数据
        List<BizReceiptDeliveryNoticeItemDTO> itemList = deliveryNoticeHeadDTO.getItemList();
        /* ================ 箱件清单 ================*/
        Map<String, List<BizReceiptDeliveryNoticeItemDTO>> groupCaseCode = itemList.stream().collect(Collectors.groupingBy(BizReceiptDeliveryNoticeItemDTO::getCaseCode));
        List<BizReceiptDeliveryNoticeItemDTO> caseList = new ArrayList<>();
        //7.18 需求变更，箱与单据绑定关系变更，为维护之前数据显示修改
        AtomicInteger rid = new AtomicInteger(1);
        deliveryNoticeHeadDTO.getCaseNowList().forEach(obj->{
            switch (obj.getPackageType()){
                case "1":
                    obj.setPackageType("纸箱");
                    obj.setPackageTypeStr("纸箱");
                    break;
                case "2":
                    obj.setPackageType("木箱");
                    obj.setPackageTypeStr("木箱");
                    break;
                case "3":
                    obj.setPackageType("裸装");
                    obj.setPackageTypeStr("裸装");
                    break;
                case "4":
                    obj.setPackageType("罐车");
                    obj.setPackageTypeStr("罐车");
                    break;
                case "5":
                    obj.setPackageType("钢瓶");
                    obj.setPackageTypeStr("钢瓶");
                    break;
                case "6":
                    obj.setPackageType("托盘");
                    obj.setPackageTypeStr("托盘");
                    break;
                case "7":
                    obj.setPackageType("袋装");
                    obj.setPackageTypeStr("袋装");
                    break;
                case "8":
                    obj.setPackageType("其他");
                    obj.setPackageTypeStr("其他");
                default:
                    obj.setPackageTypeStr(obj.getPackageType());
            }
            obj.setRid(rid.getAndIncrement()+"");
        });
        deliveryNoticeHeadDTO.setCaseList(caseList);
        /* ================ 箱件清单 ================*/
        deliveryNoticeHeadDTO.setReferReceiptCode(deliveryNoticeHeadDTO.getItemList().get(0).getReferReceiptCode());
        deliveryNoticeHeadDTO.setIsTranSpecialStr(deliveryNoticeHeadDTO.getIsTranSpecial()==1?"是":"否");
        deliveryNoticeHeadDTO.setIsSafeStr(deliveryNoticeHeadDTO.getIsSafe()==1?"是":"否");
        deliveryNoticeHeadDTO.setIsRadioactivityStr(deliveryNoticeHeadDTO.getIsRadioactivity()==1?"是":"否");
        deliveryNoticeHeadDTO.setIsDangerStr(deliveryNoticeHeadDTO.getIsDanger()==1?"是":"否");
        deliveryNoticeHeadDTO.setIsSpecialRequStr(deliveryNoticeHeadDTO.getIsSpecialRequ()==1?"是":"否");
        deliveryNoticeHeadDTO.setIsReportStr(deliveryNoticeHeadDTO.getIsReport()==1?"是":"否");
        // 送货车型
        switch (deliveryNoticeHeadDTO.getDeliveryCarType()){
            case "1":
                deliveryNoticeHeadDTO.setDeliveryCarTypeStr("板车小于6米");
                break;
            case "2":
                deliveryNoticeHeadDTO.setDeliveryCarTypeStr("板车大于6米小于13米");
                break;
            case "3":
                deliveryNoticeHeadDTO.setDeliveryCarTypeStr("板车大于13米");
                break;
            case "4":
                deliveryNoticeHeadDTO.setDeliveryCarTypeStr("厢式货车大于6米");
                break;
            case "5":
                deliveryNoticeHeadDTO.setDeliveryCarTypeStr("厢式货车小于6米");
                break;
            default:
                deliveryNoticeHeadDTO.setDeliveryCarTypeStr("");

        }

        // 分物资返运填充固定资产物料描述属性
        itemList.stream().filter(itemDTO -> itemDTO.getMatId() == 0L).forEach(item -> {
            ErpPurchaseReceiptItem erpPurchaseReceiptItem = erpPurchaseReceiptItemDataWrap.getOne(new QueryWrapper<ErpPurchaseReceiptItem>().eq(null != item.getReferReceiptItemId(), "id", item.getReferReceiptItemId()));
            if (erpPurchaseReceiptItem != null && erpPurchaseReceiptItem.getSubjectType().equals("1")){
                item.setMatName(erpPurchaseReceiptItem.getMatNameBack());
            }
        });
        
        // 设置按钮组权限
        ButtonVO buttonVO = this.setButton(deliveryNoticeHeadDTO, ctx.getCurrentUser());
        // 设置送货通知单详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO,
                new BizResultVO<>(deliveryNoticeHeadDTO, new ExtendVO(), buttonVO));
    }


    /**
     * 保存-校验送货通知入参
     *
     * @in ctx 入参 {@link BizReceiptDeliveryNoticeHeadDTO : "送货通知单"}
     */
    public void checkSaveData(BizContext ctx) {
        // 入参上下文
        BizReceiptDeliveryNoticeHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 抬头数据是否为空
        if (po == null) {
            log.warn("提交的出库单没有包含抬头信息。");
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 行项目数据是否为空
        if (UtilCollection.isEmpty(po.getItemList())) {
            log.warn("提交的验收单没有包含行项目信息。");
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }

        // 设置行项目号
        AtomicInteger rid = new AtomicInteger(1);
        for (BizReceiptDeliveryNoticeItemDTO itemDto : po.getItemList()) {
            itemDto.setRid(String.format("%05d",rid.getAndIncrement()*10));
        }
    }

    /**
     * 保存-送货通知单
     *
     * @in ctx 入参 {@link BizReceiptDeliveryNoticeHeadDTO : "送货通知单"}
     * @out ctx 入参 {@link BizReceiptDeliveryNoticeHeadDTO : "送货通知单"}
     */
    public void saveDeliveryNotice(BizContext ctx) {
        // 入参上下文
        BizReceiptDeliveryNoticeHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型(为空则是保存按钮操作)
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 当前用户信息
        CurrentUser user = ctx.getCurrentUser();
        /* ********************** head处理开始 *************************/
        String deliveryNoticeCode = po.getReceiptCode();
        if(UtilNumber.isEmpty(po.getId())){
            po.setCreateUserId(user.getId());
        }
        po.setModifyUserId(user.getId());
        po.setReceiptType(EnumReceiptType.DELIVERY_NOTICE.getValue());
        po.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
        // 验证是否为新增
        if (UtilNumber.isNotEmpty(po.getId())) {
            // 更新送货通知单
            deliveryNoticeHeadDataWrap.updateDtoById(po);
            // 修改前删除item
            this.deleteDeliveryNoticeItem(po);
            // 修改前删除箱关联
            this.deleteDeliveryNoticeCase(po);

            if (null == operationLogType) {
                // 设置上下文单据日志 - 修改
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                    EnumReceiptOperationType.RECEIPT_OPERATION_SAVE);
            }
        } else {
//            if(dictionaryService.getCorpCacheById(user.getCorpId()).getCorpCode().equals(Const.HL_59C0)){
//                // HL-5ST-RCBD0-DHTZ-YYZZZZ
//                deliveryNoticeCode = "HL-5ST-RCBD0-DHTZ-" + bizCommonService.getNextSequenceYear(EnumSequenceCode.SEQUENCE_DELIVERY_NOTICE.getValue());
//
//            } else {
//                deliveryNoticeCode = bizCommonService.getNextSequenceNoticeValue(EnumSequenceCode.SEQUENCE_DELIVERY_NOTICE.getValue(),po.getItemList().get(EnumRealYn.FALSE.getIntValue()).getFtyCode());
//            }
            deliveryNoticeCode = bizCommonService.getNextSequenceValueDaily(EnumSequenceCode.SEQUENCE_DELIVERY_NOTICE.getValue());
            po.setReceiptCode(deliveryNoticeCode);
            deliveryNoticeHeadDataWrap.saveDto(po);
            if (null == operationLogType) {
                // 设置上下文单据日志 - 创建
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                    EnumReceiptOperationType.RECEIPT_OPERATION_ESTABLISH);
            }
        }
        log.debug("保存送货通知单head成功!单号{},主键{},操作人{}", deliveryNoticeCode, po.getId(), user.getUserName());
        /* ********************** head处理结束 *************************/
        /* ********************** item处理开始 *************************/
        AtomicInteger rid = new AtomicInteger(1);
        for (BizReceiptDeliveryNoticeItemDTO itemDto : po.getItemList()) {

            if(UtilNumber.isNotEmpty(po.getContractTaxCode())){
                itemDto.setContractTaxCode(po.getContractTaxCode());
            }
            itemDto.setId(null);
            itemDto.setHeadId(po.getId());
            itemDto.setRid(String.format("%05d",rid.getAndIncrement()*10));
            itemDto.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
            itemDto.setTaxCodeRate((itemDto.getTaxPrice().subtract(itemDto.getNoTaxPrice())).divide(itemDto.getNoTaxPrice(), 2, RoundingMode.HALF_UP));
            itemDto.setCreateUserId(user.getId());
        }
        deliveryNoticeItemDataWrap.saveBatchDto(po.getItemList());
        for (BizReceiptDeliveryNoticeCaseRelDTO bizReceiptDeliveryNoticeCaseRelDTO : po.getCaseNowList()) {
            bizReceiptDeliveryNoticeCaseRelDTO.setId(UtilSequence.nextId());
            bizReceiptDeliveryNoticeCaseRelDTO.setHeadId(po.getId());
            bizReceiptDeliveryNoticeCaseRelDTO.setCreateTime(new Date());
            bizReceiptDeliveryNoticeCaseRelDTO.setModifyTime(new Date());
            bizReceiptDeliveryNoticeCaseRelDTO.setCreateUserId(ctx.getCurrentUser().getId());
            bizReceiptDeliveryNoticeCaseRelDTO.setModifyUserId(ctx.getCurrentUser().getId());
        }
        noticeCaseRelDataWrap.saveBatchDto(po.getCaseNowList());




        log.debug("批量保存送货通知单item成功,code{},headId{},操作人{}", deliveryNoticeCode, po.getId(), user.getUserName());
        /* ********************** item处理结束 *************************/
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, deliveryNoticeCode);
    }

    /**
     * 保存-送货通知单门到门信息
     *
     * @in ctx 入参 {@link BizReceiptDeliveryNoticeD2dItemDTO : "送货通知单D2D行"}
     * @out ctx 入参 {@link BizReceiptDeliveryNoticeD2dItemDTO : "送货通知单D2D行"}
     */
    public void saveDeliveryNoticeD2d(BizContext ctx) {
        // 入参上下文
        BizReceiptDeliveryNoticeD2dItemDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前用户信息
        CurrentUser user = ctx.getCurrentUser();
        /* ********************** item处理开始 *************************/
        if (UtilNumber.isEmpty(po.getId())) {
            // 新增
            po.setId(null);
            po.setModifyUserId(user.getId());
            deliveryNoticeD2dItemDataWrap.saveDto(po);
        } else {
            // 修改
            BizReceiptDeliveryNoticeD2dItem d2dItem = deliveryNoticeD2dItemDataWrap.getById(po.getId());
            if (UtilObject.isNull(d2dItem)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PRIMARY_KEY_NOT_EXIST, "ITEM");
            }
            po.setModifyUserId(user.getId());
            deliveryNoticeD2dItemDataWrap.updateDtoById(po);
        }
        log.debug("保存送货通知单门到门item成功,headId{},操作人{}", po.getId(), user.getUserName());
        /* ********************** item处理结束 *************************/

        // 保存操作日志
        receiptOperationLogService.saveBizReceiptOperationLogList(po.getHeadId(), EnumReceiptType.DELIVERY_NOTICE.getValue(), EnumReceiptOperationType.RECEIPT_OPERATION_UPDATE, "", ctx.getCurrentUser().getId());
    }

    /**
     * 删除-送货通知单门到门信息
     *
     * @in ctx 入参 {@link BizReceiptDeliveryNoticeD2dItemDTO : "送货通知单D2D行"}
     * @out ctx 入参 {@link BizReceiptDeliveryNoticeD2dItemDTO : "送货通知单D2D行"}
     */
    public void removeDeliveryNoticeD2d(BizContext ctx) {
        // 入参上下文
        BizReceiptDeliveryNoticeD2dItemDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前用户信息
        CurrentUser user = ctx.getCurrentUser();
        /* ********************** item处理开始 *************************/
        if (UtilNumber.isNotEmpty(po.getId())) {
            // 删除
            deliveryNoticeD2dItemDataWrap.removeById(po.getId());
        }
        log.debug("删除送货通知单门到门item成功,headId{},操作人{}", po.getId(), user.getUserName());
        /* ********************** item处理结束 *************************/

        // 保存操作日志
        receiptOperationLogService.saveBizReceiptOperationLogList(po.getHeadId(), EnumReceiptType.DELIVERY_NOTICE.getValue(), EnumReceiptOperationType.RECEIPT_OPERATION_UPDATE, "", ctx.getCurrentUser().getId());
    }

    private void deleteDeliveryNoticeCase(BizReceiptDeliveryNoticeHeadDTO po) {
        QueryWrapper<BizReceiptDeliveryNoticeCaseRel> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizReceiptDeliveryNoticeCaseRel::getHeadId,po.getId());
        noticeCaseRelDataWrap.remove(queryWrapper);
    }


    /**
     * 保存操作日志
     *
     * @in ctx 入参 {@link BizReceiptDeliveryNoticeHeadDTO : "要保存操作日志的送货通知单"}
     */
    public void saveBizReceiptOperationLog(BizContext ctx) {
        // 入参上下文 - 保存的送货通知单
        BizReceiptDeliveryNoticeHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 保存操作日志
        receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(),
            operationLogType, "", ctx.getCurrentUser().getId());
    }

    /**
     * 保存附件
     *
     * @in ctx 入参 {@link BizReceiptDeliveryNoticeHeadDTO : "要保存附件的送货通知单"}
     */
    public void saveBizReceiptAttachment(BizContext ctx) {
        // 入参上下文
        BizReceiptDeliveryNoticeHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        // 保存送货通知单附件
        receiptAttachmentService.saveBizReceiptAttachment(headDTO.getFileList(), headDTO.getId(),
            EnumReceiptType.DELIVERY_NOTICE.getValue(), user.getId());
        log.debug("保存送货通知单附件成功!");
    }

    /**
     * 保存单据流
     *
     * @in ctx 入参 {@link BizReceiptDeliveryNoticeHeadDTO : "要保存单据流的送货通知单"}
     */
    public void saveReceiptTree(BizContext ctx) {
        // 入参上下文
        BizReceiptDeliveryNoticeHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizCommonReceiptRelation> dtoList = new ArrayList<>();
        for (BizReceiptDeliveryNoticeItemDTO item : headDTO.getItemList()) {
            BizCommonReceiptRelation dto = new BizCommonReceiptRelation();
            dto.setReceiptType(headDTO.getReceiptType());
            dto.setReceiptHeadId(item.getHeadId());
            dto.setReceiptItemId(item.getId());
            dto.setPreReceiptType(item.getPreReceiptType());
            dto.setPreReceiptHeadId(item.getPreReceiptHeadId());
            dto.setPreReceiptItemId(item.getPreReceiptItemId());
            dtoList.add(dto);
        }
        receiptRelationService.multiSaveReceiptTree(dtoList);
    }

    /**
     * 提交-校验送货通知入参
     *
     * @in ctx 入参 {@link BizReceiptDeliveryNoticeHeadDTO : "送货通知单"}
     */
    public void checkSubmitData(BizContext ctx) {
        // 入参上下文
        BizReceiptDeliveryNoticeHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        /* ******** 校验入参 ******** */
        this.checkSaveData(ctx);
        /* ******** 校验计划到货日期不能为空 ******** */
//        if (UtilObject.isNull(headDTO.getPlanArrivalDate())) {
//            throw new WmsException(EnumReturnMsg.RETURN_CODE_PLANNED_ARRIVAL_DATE_IS_EMPTY);
//        }
        /* ******** 校验数量 ******** */
        this.checkItemQty(headDTO);


        // 油品送货 校验合同有效期，到达“有效期截止日期”时，则不允再次创建油品送货单
        if(EnumSendType.OIL_PROCUREMENT.getValue().equals(headDTO.getSendType())){
            this.checkContractValidity(headDTO);
        }
        // 离岸送货/内陆送货 校验批次号是否存在   
        if(EnumSendType.OFFSHORE_PROCUREMENT.getValue().equals(headDTO.getSendType())||EnumSendType.INLAND_PROCUREMENT.getValue().equals(headDTO.getSendType())){
            this.checkBatchNo(headDTO);
        }
        // 校验按类别去重后的费用占比之和等于100%
        if(EnumSendType.OFFSHORE_PROCUREMENT.getValue().equals(headDTO.getSendType())){
            // 需求变更 bug 30610
            //this.checkFeeRatio(headDTO);
        }
        // [31141]【内贸送货】同物料编码的内贸倍率必须一致，提交时进行校验；同物料编码的内贸倍率一致，则提交成功；不一致，则提示：物料编码XXXXX的内贸倍率不一致，请修改
        if (EnumSendType.INLAND_PROCUREMENT.getValue().equals(headDTO.getSendType())) {
            Map<Long, BigDecimal> map = new HashMap<>();
            for (BizReceiptDeliveryNoticeItemDTO itemDTO : headDTO.getItemList()) {
                Long key = itemDTO.getMatId();
                BigDecimal value = itemDTO.getNyTaxRate();
                if (map.isEmpty() || !map.containsKey(key)) {
                    map.put(key, value);
                } else if (map.get(key).compareTo(value) != 0) {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_INCONSISTENT_DOMESTIC_TRADE_RATE, itemDTO.getMatCode());
                }
            }
        }
        // [31240]【油品送货】油品送货单重复提交，生成了3个采购订单以及到货登记单，系统需要做一下卡控。
        if (UtilNumber.isNotEmpty(headDTO.getId())) {
            BizReceiptDeliveryNoticeHead oldHead = deliveryNoticeHeadDataWrap.getById(headDTO.getId());
            if (!(oldHead.getReceiptStatus().equals(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue()) || oldHead.getReceiptStatus().equals(EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue()))
                    || !oldHead.getModifyTime().equals(headDTO.getModifyTime())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_HAVE_SUBMIT);
            }
        }
    }

    /**
     * 校验按类别去重后的费用占比之和等于100%
     */
    public void checkFeeRatio(BizReceiptDeliveryNoticeHeadDTO headDTO){

        List<BizReceiptDeliveryNoticeItemDTO> itemList = headDTO.getItemList();
        
        for(BizReceiptDeliveryNoticeItemDTO item : itemList){
            if(UtilString.isNullOrEmpty(item.getMatTypeStr())){
                throw new WmsException(EnumReturnMsg.RETURN_CODE_MAT_TYPE_IS_EMPTY);
            }
            if(UtilNumber.isEmpty(item.getFeeRatio())){
                throw new WmsException(EnumReturnMsg.RETURN_CODE_FEE_RATIO_IS_EMPTY);
            }   
        }

        Map<String, List<BizReceiptDeliveryNoticeItemDTO>> matTypeFeeRatioMap = headDTO.getItemList().stream()
            .filter(item -> UtilString.isNotNullOrEmpty(item.getMatTypeStr()))
            .collect(Collectors.groupingBy(BizReceiptDeliveryNoticeItemDTO::getMatTypeStr));  

        matTypeFeeRatioMap.forEach((matTypeStr, innerItemList) -> {

            // 所有物料类型费用比例必须相等
            BigDecimal feeRatio = innerItemList.get(0).getFeeRatio();
            if(innerItemList.stream().anyMatch(item -> !item.getFeeRatio().equals(feeRatio))){
                throw new WmsException(EnumReturnMsg.RETURN_CODE_FEE_RATIO_ERROR_BY_MAT_TYPE, 
                matTypeStr);
            }
        }); 

        // 每种物料类型取第一个进行汇总 
        BigDecimal totalFeeRatio = matTypeFeeRatioMap.values().stream()
            .map(innerItemList -> innerItemList.get(0).getFeeRatio())
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        if(totalFeeRatio.compareTo(new BigDecimal("100")) != 0){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_FEE_RATIO_ERROR);
        }   
    }

    /**
     * 离岸送货 校验批次号是否存在
     *
     * @in headDTO 入参 {@link BizReceiptDeliveryNoticeHeadDTO : "送货通知单"}
     */
    private void checkBatchNo(BizReceiptDeliveryNoticeHeadDTO headDTO){


        // 根据批次号查询送货通知抬头
        List<BizReceiptDeliveryNoticeHead> deliveryNoticeHeadList = deliveryNoticeHeadDataWrap.list(new LambdaQueryWrapper<BizReceiptDeliveryNoticeHead>()
            .eq(BizReceiptDeliveryNoticeHead::getBatchCode, headDTO.getBatchCode())
            .ne(UtilNumber.isNotEmpty(headDTO.getId()), BizReceiptDeliveryNoticeHead::getId, headDTO.getId()));   

        if(UtilCollection.isNotEmpty(deliveryNoticeHeadList)){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_BATCH_CODE_EXIST, headDTO.getBatchCode());
        }
    }

    public void checkContractValidity(BizReceiptDeliveryNoticeHeadDTO headDTO){

        Long contractId = headDTO.getContractId();
        if(UtilNumber.isNotEmpty(contractId)){
            BizReceiptContractHead contractHead = bizReceiptContractHeadDataWrap.getById(contractId);
            // [31244]【油品送货】取消油品送货的框架协议“有效期截止”限制，只校验所选框架是否未“执行中”。执行中的框架都允许送货
            if(!EnumReceiptStatus.RECEIPT_STATUS_EXECUTING.getValue().equals(contractHead.getReceiptStatus())){
                throw new WmsException(EnumReturnMsg.RETURN_CODE_CONTRACT_VALIDITY_EXPIRED);
            }
            // 在提交油品送货单时，校验各行项目中的“发票日期”是否包含在“抬头的框架协议有效期内”，在，则允许提交；不在则提交用户“发票日期不正确”
            if (UtilNumber.isNotEmpty(headDTO.getIsVehicleInvoice()) && headDTO.getIsVehicleInvoice().equals(1)) {
                headDTO.getItemList().forEach(item -> {
                    if (!UtilDate.checkDate(item.getInvoiceDate(), contractHead.getValidityStartDate(), contractHead.getValidityEndDate())) {
                        throw new WmsException("发票日期不正确");
                    }
                });
            }
        }
    }

    /**
     * 提交送货通知
     *
     * @in ctx 入参 {@link BizReceiptDeliveryNoticeHeadDTO : "提交送货通知"}
     * @out ctx 出参 {@link BizReceiptDeliveryNoticeHeadDTO : "提交的送货通知")}
     */
    public void submitDeliveryNotice(BizContext ctx) {
        // 入参上下文
        BizReceiptDeliveryNoticeHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT);
        // 保存送货通知
        this.saveDeliveryNotice(ctx);
        // 更新送货通知head、item状态 - 已完成
        if(EnumSendType.OFFSHORE_PROCUREMENT.getValue().equals(po.getSendType())){
            this.updateStatus(po, po.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue());
        }else{
            this.updateStatus(po, po.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
        }
        
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, po);
    }

    /**
     * 删除前校验
     *
     * @in ctx 入参 {@link BizReceiptDeliveryNoticeDeletePO : "送货通知单删除入参"}
     * @out ctx 出参 {@link BizReceiptDeliveryNoticeDeletePO : "如果是全部删除，设置行项目id集合"}
     */
    public void checkDeleteDeliveryNotice(BizContext ctx) {
        // 入参上下文
        BizReceiptDeliveryNoticeDeletePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilObject.isNull(po) || UtilNumber.isEmpty(po.getHeadId())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取送货通知信息
        BizReceiptDeliveryNoticeHead deliveryNoticeHead = deliveryNoticeHeadDataWrap.getById(po.getHeadId());
        // 转DTO
        BizReceiptDeliveryNoticeHeadDTO deliveryNoticeHeadDTO =
            UtilBean.newInstance(deliveryNoticeHead, BizReceiptDeliveryNoticeHeadDTO.class);
        // 填充父子属性
        dataFillService.fillSonAttrForDataObj(deliveryNoticeHeadDTO);
        /* ******** 校验送货通知单head ******** */
        if (UtilObject.isNotNull(deliveryNoticeHeadDTO)) {
            if (EnumReceiptStatus.RECEIPT_STATUS_SUBMITTED.getValue()
                .equals(deliveryNoticeHeadDTO.getReceiptStatus())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_NO_AUTHORITY_DELETE);
            }
        } else {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_NOT_EXIST);
        }
        /* ******** 校验是否全部删除 ******** */
        if (po.isDeleteAll()) {
            po.setItemIds(deliveryNoticeHeadDTO.getItemList().stream().map(BizReceiptDeliveryNoticeItemDTO::getId)
                .collect(Collectors.toList()));
        } else if (UtilCollection.isEmpty(po.getItemIds())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
    }

    /**
     * 删除送货通知单
     *
     * @in ctx 入参 {@link BizReceiptDeliveryNoticeDeletePO : "送货通知单删除入参"}
     */
    public void deleteDeliveryNotice(BizContext ctx) {
        // 入参上下文
        BizReceiptDeliveryNoticeDeletePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        /* ******** 删除送货通知 ******** */
        if (po.isDeleteAll()) {
            // 删除送货通知head
            deliveryNoticeHeadDataWrap.removeById(po.getHeadId());
            // 删除送货通知item
            UpdateWrapper<BizReceiptDeliveryNoticeItem> wrapper = new UpdateWrapper<>();
            wrapper.lambda().eq(BizReceiptDeliveryNoticeItem::getHeadId, po.getHeadId());
            deliveryNoticeItemDataWrap.remove(wrapper);
            // 保存操作日志 - 删除
            receiptOperationLogService.saveBizReceiptOperationLogList(po.getHeadId(),
                EnumReceiptType.DELIVERY_NOTICE.getValue(), EnumReceiptOperationType.RECEIPT_OPERATION_DELETE, "",
                ctx.getCurrentUser().getId());
        } else {
            // 删除送货通知item
            deliveryNoticeItemDataWrap.removeByIds(po.getItemIds());
        }
    }

    /**
     * 删除单据流
     *
     * @in ctx 入参 {@link BizReceiptDeliveryNoticeDeletePO : "送货通知删除入参"}
     */
    public void deleteReceiptTree(BizContext ctx) {
        // 入参上下文
        BizReceiptDeliveryNoticeDeletePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (po.isDeleteAll()) {
            // 删除单据流
            receiptRelationService.deleteReceiptTree(EnumReceiptType.DELIVERY_NOTICE.getValue(), po.getHeadId());
        }
    }

    /**
     * 删除单据附件
     *
     * @in ctx 入参 {@link BizReceiptDeliveryNoticeDeletePO : "送货通知删除入参"}
     */
    public void deleteReceiptAttachment(BizContext ctx) {
        // 入参上下文
        BizReceiptDeliveryNoticeDeletePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (po.isDeleteAll()) {
            // 删除单据附件
            receiptAttachmentService.deleteBizReceiptAttachment(po.getHeadId(),
                EnumReceiptType.DELIVERY_NOTICE.getValue());
        }
    }

    /**
     * 添加物料查询-基于采购订单
     *
     * @in ctx 入参 {@link BizReceiptPreSearchPO : "查询条件"}
     * @out ctx 出参 {@link MultiResultVO<BizReceiptDeliveryNoticePreHeadVo> :"送货通知单前续单据结果集"}
     */
    public void purchaseReceipt(BizContext ctx) {
        // 入参上下文
        BizReceiptPreSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        if (EnumReceiptType.PURCHASE_RECEIPT.getValue().equals(po.getPreReceiptType())) {
            // 装载返回对象
            MultiResultVO<BizReceiptDeliveryNoticePreHeadVo> returnVo = new MultiResultVO<>();
            // 调用SAP查询采购订单
            List<ErpPurchaseReceiptItemDTO> purchaseReceiptItemVoList = purchaseReceiptService
                .getErpPurchaseReceiptItemList(po.setIsReturnFlag(EnumRealYn.FALSE.getIntValue()), user);
            // 上下文返回参数
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, this.purchaseDataFormat(returnVo, purchaseReceiptItemVoList));
        }
    }

/*    *//**
     * 完成送货前校验
     *
     * @in ctx 入参 {@link BizReceiptDeliveryNoticeHeadDTO : "送货通知单"}
     *//*
    public void checkFinishDelivery(BizContext ctx) {
        // 入参上下文
        BizReceiptDeliveryNoticeHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        *//* ******** 校验入参 ******** *//*
        this.checkSaveData(ctx);
        if (!EnumReceiptStatus.RECEIPT_STATUS_IN_DELIVERY.getValue().equals(po.getReceiptStatus())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_NO_AUTHORITY_OPERATION);
        }
    }*/

/*    *//**
     * 完成送货
     *
     * @in ctx 入参 {@link BizReceiptDeliveryNoticeHeadDTO : "送货通知单"}
     *//*
    @Transactional(rollbackFor = Exception.class)
    public void finishDelivery(BizContext ctx) {
        // 入参上下文
        BizReceiptDeliveryNoticeHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 更新送货通知单head、item状态-已完成
        this.updateStatus(po, po.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
        // 返回上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, po.getReceiptCode());
    }*/

    /**
     * 采购订单查询参转换
     *
     * @param returnVo 要设置的返回数据
     * @param purchaseReceiptItemVoList 采购订单信息
     * @return 物料信息
     */
    private MultiResultVO<BizReceiptDeliveryNoticePreHeadVo> purchaseDataFormat(
        MultiResultVO<BizReceiptDeliveryNoticePreHeadVo> returnVo,
        List<ErpPurchaseReceiptItemDTO> purchaseReceiptItemVoList) {
        if (UtilCollection.isNotEmpty(purchaseReceiptItemVoList)) {
            // 装载返回数据
            List<BizReceiptDeliveryNoticePreHeadVo> headInfoList = new ArrayList<>();
            // 根据采购订单号分组
            LinkedHashMap<String, List<ErpPurchaseReceiptItemDTO>> purchaseMap =
                purchaseReceiptItemVoList.stream().collect(Collectors
                    .groupingBy(ErpPurchaseReceiptItemDTO::getReceiptCode, LinkedHashMap::new, Collectors.toList()));
            Set<String> keys = purchaseMap.keySet();
            for (String key : keys) {
                // 装载返回数据head
                BizReceiptDeliveryNoticePreHeadVo headInfo = new BizReceiptDeliveryNoticePreHeadVo();
                // 装载返回数据item
                List<BizReceiptDeliveryNoticeItemDTO> itemInfoList = new ArrayList<>();
                List<ErpPurchaseReceiptItemDTO> purchaseItemList = purchaseMap.get(key);
                for (int i = 0; i < purchaseItemList.size(); i++) {
                    ErpPurchaseReceiptItemDTO purchaseDTO = purchaseItemList.get(i);
                    BizReceiptDeliveryNoticeItemDTO itemInfo = new BizReceiptDeliveryNoticeItemDTO();
                    /* ******** 设置head列字段 ******** */
                    if (i == 0) {
                        headInfo = UtilBean.newInstance(purchaseDTO, headInfo.getClass());
                        headInfo.setReferReceiptCode(purchaseDTO.getReceiptCode());
                        headInfo.setPurchaseUserCode(purchaseDTO.getPurchaseUserCode());
                        headInfo.setPurchaseUserName(purchaseDTO.getPurchaseUserName());
                        headInfo.setContractCode(purchaseDTO.getContractCode());
                        headInfo.setContractName(purchaseDTO.getContractName());
                        headInfo.setSupplierCode(purchaseDTO.getSupplierCode());
                        headInfo.setSupplierName(purchaseDTO.getSupplierName());
                    }
                    /* ******** 设置item列字段 ******** */
                    itemInfo = UtilBean.newInstance(purchaseDTO, itemInfo.getClass());
                    itemInfo.setId(null);
                    itemInfo.setHeadId(null);
                    itemInfo.setRid(Const.STRING_EMPTY);
                    itemInfo.setReceiptCode(Const.STRING_EMPTY);
                    itemInfo.setReferReceiptCode(purchaseDTO.getReceiptCode());
                    itemInfo.setReferReceiptRid(purchaseDTO.getRid());
                    itemInfo.setPreReceiptHeadId(purchaseDTO.getHeadId());
                    itemInfo.setPreReceiptItemId(purchaseDTO.getId());
                    itemInfo.setPreReceiptType(EnumReceiptType.PURCHASE_RECEIPT.getValue());
                    itemInfo.setPreReceiptQty(purchaseDTO.getReceiptQty());
                    itemInfo.setReferReceiptHeadId(purchaseDTO.getHeadId());
                    itemInfo.setReferReceiptItemId(purchaseDTO.getId());
                    itemInfo.setReferReceiptType(EnumReceiptType.PURCHASE_RECEIPT.getValue());
                    itemInfo.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue());
                    // 获取缓存中仓库信息
                    itemInfo.setWhId(UtilObject.isNotNull(dictionaryService.getLocationCacheById(purchaseDTO.getLocationId()))
                            ? dictionaryService.getLocationCacheById(purchaseDTO.getLocationId()).getWhId() : 0L);
                    itemInfoList.add(itemInfo);
                }
                headInfo.setChildren(itemInfoList);
                headInfoList.add(headInfo);
            }
            returnVo.setResultList(headInfoList);
        }
        return returnVo;
    }

    /**
     * 设置已送货数量
     *
     * @param referReceiptItemId 参考单据行项目id
     * @return 已送货数量
     */
    private BigDecimal setSendQty(Long referReceiptItemId) {
        if (UtilNumber.isEmpty(referReceiptItemId)) {
            return BigDecimal.ZERO;
        }
        QueryWrapper<BizReceiptDeliveryNoticeItem> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(BizReceiptDeliveryNoticeItem::getReferReceiptItemId, referReceiptItemId)
            .eq(BizReceiptDeliveryNoticeItem::getItemStatus, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
        // 根据参考单据获取送货通知单
        List<BizReceiptDeliveryNoticeItem> itemList = deliveryNoticeItemDataWrap.list(wrapper);
        // 已送货数量
        BigDecimal sendQty = BigDecimal.ZERO;
        for (BizReceiptDeliveryNoticeItem item : itemList) {
            sendQty = sendQty.add(item.getQty());
        }
        return sendQty;
    }

    /**
     * 按钮组
     *
     * @param headDTO 送货通知单
     * @return 按钮组对象
     */
    private ButtonVO setButton(BizReceiptDeliveryNoticeHeadDTO headDTO, CurrentUser user) {
        // 单据抬头状态
        Integer receiptStatus = headDTO.getReceiptStatus();
        if (UtilObject.isNull(receiptStatus)) {
            return new ButtonVO();
        }
        ButtonVO buttonVO = new ButtonVO();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 草稿 -【保存、提交、删除】
            return buttonVO.setButtonSave(true).setButtonSubmit(true).setButtonDelete(true).setButtonDeliveryNoticeApprove(this.checkUserFactory(user));
        }else if (EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(receiptStatus)) {
            if(headDTO.getSendType().equals(EnumSendType.INLAND_PROCUREMENT.getValue())){
                // [30563]【内贸送货】打开已完成的内贸通知单，请添加：撤销按钮。
                buttonVO.setButtonRevoke(true).setButtonClose(true).setButtonPrint(true);
//                // [B10658]【内贸送货】新增“门到门信息”模块，添加：保存按钮。
//                if (EnumRealYn.TRUE.getIntValue().equals(headDTO.getIsD2dDelivery())) {
//                    buttonVO.setButtonSave(true);
//                }
                return buttonVO;
            } else
            // 已完成 关闭按钮
            if(!headDTO.getSendType().equals(EnumSendType.OFFSHORE_PROCUREMENT.getValue())){
                return buttonVO.setButtonClose(true);
            }else{
                return buttonVO.setButtonRevoke(true);
            }

        }else if (EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue().equals(receiptStatus)) {
            // 未同步 提交按钮
            return buttonVO.setButtonPost(true);
            
        }else if (EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue().equals(receiptStatus)) {
            // 已驳回 -【提交】
            return buttonVO.setButtonSubmit(true);
        }else if (EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue().equals(receiptStatus)) {
            // 审批中 -【撤销】
            return buttonVO.setButtonRevoke(true);
        }else if (EnumReceiptStatus.RECEIPT_STATUS_CLOSED.getValue().equals(receiptStatus)) {
            // 已关闭 
            return buttonVO;
        }
        return buttonVO;
    }

    /**
     * 设置列表、分页查询条件
     *
     * @param po 查询条件对象
     * @return QueryWrapper<BizReceiptDeliveryNoticeHead>
     */
    private WmsQueryWrapper<BizReceiptDeliveryNoticeSearchPO> setQueryWrapper(BizReceiptDeliveryNoticeSearchPO po,CurrentUser user) {
        if (null == po) {
            po = new BizReceiptDeliveryNoticeSearchPO();
        }

        // JS01，JS03，JS04的角色 可以查询所有供应商合同    
        // 当前登录人
        
        if(po.getSendType().equals(EnumSendType.OIL_PROCUREMENT.getValue())||po.getSendType().equals(EnumSendType.ONSHORE_PROCUREMENT.getValue())){
            this.setAuth(po,user);
        }
            


        List<Long> locationIdList =null;
        if(user!=null &&user.getLocationList()!=null){
            List<DicStockLocationDTO> locationList =user.getLocationList();
            locationIdList =locationList.stream().map(DicStockLocationDTO::getId).collect(Collectors.toList());
        }
        String deliveryNoticeDesc = po.getDeliveryNoticeDescribe();
        // 查询条件设置
        WmsQueryWrapper<BizReceiptDeliveryNoticeSearchPO> wrapper = new WmsQueryWrapper<>();
        wrapper.lambda()
                .eq(true, BizReceiptDeliveryNoticeSearchPO::getIsDelete,
                        BizReceiptDeliveryNoticeHead.class, 0)
                .likeLeft(UtilString.isNotNullOrEmpty(po.getReceiptCode()), BizReceiptDeliveryNoticeSearchPO::getReceiptCode,
                        BizReceiptDeliveryNoticeHead.class, po.getReceiptCode())
                .eq(UtilNumber.isNotEmpty(po.getReceiptType()), BizReceiptDeliveryNoticeSearchPO::getReceiptType,
                        BizReceiptDeliveryNoticeHead.class, po.getReceiptType())
                .in(UtilCollection.isNotEmpty(po.getReceiptStatusList()), BizReceiptDeliveryNoticeSearchPO::getReceiptStatus,
                        BizReceiptDeliveryNoticeHead.class, po.getReceiptStatusList())
                .in(UtilCollection.isNotEmpty(locationIdList),  BizReceiptDeliveryNoticeSearchPO::getLocationId,
                        BizReceiptDeliveryNoticeItem.class,locationIdList)
                .eq(UtilString.isNotNullOrEmpty(po.getPurchaseReceiptCode()), BizReceiptDeliveryNoticeSearchPO::getPurchaseCode,
                        BizReceiptDeliveryNoticeHead.class, po.getPurchaseReceiptCode())
                .eq(UtilString.isNotNullOrEmpty(po.getMatCode()), BizReceiptDeliveryNoticeSearchPO::getMatCode,
                        DicMaterial.class, po.getMatCode())
                .like(UtilString.isNotNullOrEmpty(po.getCreateUserName()), BizReceiptDeliveryNoticeSearchPO::getUserName,
                        SysUser.class, po.getCreateUserName())
                .between((UtilObject.isNotNull(po.getStartTime()) && UtilObject.isNotNull(po.getEndTime())), BizReceiptDeliveryNoticeSearchPO::getCreateTime,
                        BizReceiptDeliveryNoticeHead.class, po.getStartTime(), po.getEndTime())
                .like(UtilString.isNotNullOrEmpty(deliveryNoticeDesc), BizReceiptDeliveryNoticeSearchPO::getDeliveryNoticeDescribe,
                        BizReceiptDeliveryNoticeHead.class, deliveryNoticeDesc)
                .like(UtilString.isNotNullOrEmpty(po.getContractCode()), BizReceiptDeliveryNoticeSearchPO::getReceiptCode,
                        BizReceiptContractHead.class, po.getContractCode())
                .like(UtilString.isNotNullOrEmpty(po.getPurchaserName()), BizReceiptDeliveryNoticeSearchPO::getCreateUserName,
                        BizReceiptContractHead.class, po.getPurchaserName())
                .eq(UtilNumber.isNotEmpty(po.getSendType()), BizReceiptDeliveryNoticeSearchPO::getSendType,
                        BizReceiptDeliveryNoticeHead.class, po.getSendType())
                .eq(UtilNumber.isNotEmpty(po.getSupplierId()), BizReceiptDeliveryNoticeSearchPO::getSupplierId,
                        BizReceiptContractHead.class, po.getSupplierId()) 
        ;
        return wrapper;
    }

    private void setAuth(BizReceiptDeliveryNoticeSearchPO po,CurrentUser user){
        Set<String> userRoleSet = user.getSysUserRoleRelList().stream().map(SysUserRoleRel::getRoleCode).collect(Collectors.toSet());

        Set<String> roleSet = new HashSet<>();
        roleSet.add("JS01");
        roleSet.add("JS03");
        roleSet.add("JS04");

        // 取交集
        userRoleSet.retainAll(roleSet);

        if(!userRoleSet.isEmpty()){
                // JS01，JS03，JS04的角色 可以查询所有供应商合同
            po.setSupplierId(null);
        }else{
            // 供应商只能查询自己供应商合同
            List<DicSupplierUserRel> supplierUserRelList = bidderUserRelDataWrap.list(new QueryWrapper<DicSupplierUserRel>() {{
                    lambda().eq(DicSupplierUserRel::getUserId, user.getId());
                }});
            if (UtilCollection.isNotEmpty(supplierUserRelList)) {
                po.setSupplierId(supplierUserRelList.get(0).getSupplierId());
            }else{
                po.setSupplierId(-1L);
            }
        }
    }

    /**
     * 删除送货通知单行项目
     *
     * @param headDTO 送货通知
     */
    private void deleteDeliveryNoticeItem(BizReceiptDeliveryNoticeHeadDTO headDTO) {
        UpdateWrapper<BizReceiptDeliveryNoticeItem> wrapper = new UpdateWrapper<>();
        wrapper.lambda().eq(BizReceiptDeliveryNoticeItem::getHeadId, headDTO.getId());
        deliveryNoticeItemDataWrap.physicalDelete(wrapper);
    }

    /**
     * 删除送货通知单门到门行项目
     *
     * @param headDTO 送货通知
     */
    private void deleteDeliveryNoticeD2dItem(BizReceiptDeliveryNoticeHeadDTO headDTO) {
        UpdateWrapper<BizReceiptDeliveryNoticeD2dItem> wrapper = new UpdateWrapper<>();
        wrapper.lambda().eq(BizReceiptDeliveryNoticeD2dItem::getHeadId, headDTO.getId());
        deliveryNoticeD2dItemDataWrap.physicalDelete(wrapper);
    }

    /**
     * 校验行项目提交数量
     *
     * @param headDTO 送货通知单
     */
    private void checkItemQty(BizReceiptDeliveryNoticeHeadDTO headDTO) {
        // 装载qty为零的行项目
        List<String> errorQtyZeroList = new ArrayList<>();
        // 装载qty已超过送货数量的行项目
        List<String> errorQtyExceedList = new ArrayList<>();

        this.refreshContractQty(headDTO);
        BigDecimal sum = BigDecimal.ZERO;
        for (BizReceiptDeliveryNoticeItemDTO itemDTO : headDTO.getItemList()) {
            if (itemDTO.getQty().compareTo(BigDecimal.ZERO) == 0) {
                errorQtyZeroList.add(itemDTO.getRid());
                return;
            }
            sum = sum.add(itemDTO.getQty());
        }
        if (UtilCollection.isNotEmpty(errorQtyZeroList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_OPERATION_QTY_ZERO, errorQtyZeroList.toString());
        }
        if (UtilCollection.isNotEmpty(errorQtyExceedList) && !headDTO.getSendType().equals(EnumSendType.OIL_PROCUREMENT.getValue())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EXCEED_DELIVERY_QTY, errorQtyExceedList.toString());
        }
        if (headDTO.getSendType().equals(EnumSendType.OIL_PROCUREMENT.getValue()) && headDTO.getCanDeliveryQty().compareTo(sum) < 0) {
            throw new WmsException("当前合同的可送货数量不足，请联系采购经理");
        }
    }

    /**
     * 准备更新送货通知状态
     *
     * @param headDTO 送货通知head
     * @param itemDTOList 送货通知item
     */
    public void updateStatus(BizReceiptDeliveryNoticeHeadDTO headDTO, List<BizReceiptDeliveryNoticeItemDTO> itemDTOList,
        Integer status) {
        if (UtilObject.isNull(headDTO)) {
            // 更新item状态
            itemDTOList.forEach(item -> item.setItemStatus(status));
            this.updateItem(itemDTOList);
        } else if (UtilCollection.isEmpty(itemDTOList)) {
            // 更新head状态
            headDTO.setReceiptStatus(status);
            this.updateHead(headDTO);
        } else if (UtilCollection.isNotEmpty(itemDTOList)) {
            // 更新head、item状态
            itemDTOList.forEach(item -> item.setItemStatus(status));
            this.updateItem(itemDTOList);
            headDTO.setReceiptStatus(status);
            this.updateHead(headDTO);
        }
    }

    /**
     * 更新送货通知head状态
     *
     * @param headDto 送货通知head
     */
    private void updateHead(BizReceiptDeliveryNoticeHeadDTO headDto) {
        if (UtilObject.isNotNull(headDto)) {
            deliveryNoticeHeadDataWrap.updateDtoById(headDto);
        }
    }

    /**
     * 更新送货通知item状态
     *
     * @param itemDtoList 送货通知item
     */
    private void updateItem(List<BizReceiptDeliveryNoticeItemDTO> itemDtoList) {
        if (UtilCollection.isNotEmpty(itemDtoList)) {
            deliveryNoticeItemDataWrap.updateBatchDtoById(itemDtoList);
        }
    }

    /**
     * 生成到货登记单
     *
     * @param ctx - 到货通知单提交表单内容
     */
    public void genArrivalRegister(BizContext ctx) {
        // 入参上下文
        BizReceiptDeliveryNoticeHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        
        // 改为自动创建
        // if(po.getSendType()==3||po.getSendType()==2){
        //     // 油品采购，在岸采购不自动生成到货登记 需手动创建
        //     return;
        // }

        // 资产类合同不生成到货登记单
        
        Long contractId = po.getItemList().get(0).getReferReceiptHeadId();
        BizReceiptContractHead contractHead = bizReceiptContractHeadDataWrap.getById(contractId);
        if(EnumPurchaseType.ASSET.getCode().equals(contractHead.getPurchaseType())){
            return;
        }

        // 组装参数
        BizReceiptRegisterHeadDTO headDTO = new BizReceiptRegisterHeadDTO();
        List<BizReceiptRegisterItemDTO> itemDTOList = new ArrayList<>();
        headDTO.setReceiptType(EnumReceiptType.ARRIVAL_REGISTER.getValue())
                .setDeliveryNoticeDescribe(po.getDeliveryNoticeDescribe())
                .setContractId(po.getContractId())
                .setBatchCode(po.getBatchCode())
                .setPurchaseCode(po.getPurchaseCode())
                .setSendType(po.getSendType())
                .setTransportType(po.getTransportType())
                .setTransportBatch(po.getTransportBatch())
                .setDriverInfo(po.getDriverInfo())
                .setCarInfo(po.getCarInfo())
                .setContactWay(po.getContactWay())
        ;
        headDTO.setCaseNowList(po.getCaseNowList());
        for (BizReceiptDeliveryNoticeItemDTO itemDTO : po.getItemList()) {
            BizReceiptRegisterItemDTO receiptRegisterItemDTO = UtilBean.newInstance(itemDTO, BizReceiptRegisterItemDTO.class);
            receiptRegisterItemDTO.setPreReceiptHeadId(po.getId());
            receiptRegisterItemDTO.setPreReceiptItemId(itemDTO.getId());
            receiptRegisterItemDTO.setPreReceiptType(EnumReceiptType.DELIVERY_NOTICE.getValue());
            receiptRegisterItemDTO.setDeliveryItemId(itemDTO.getId());
            receiptRegisterItemDTO.setPreReceiptQty(itemDTO.getQty());
            itemDTOList.add(receiptRegisterItemDTO);
        }
        headDTO.setItemList(itemDTOList);
        // 设置入参上下文
        BizContext ctxRegister = new BizContext();
        ctxRegister.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
        ctxRegister.setCurrentUser(ctx.getCurrentUser());
        // 推送MQ生成到货登记单
        ProducerMessageContent message = ProducerMessageContent.messageContent(TagConst.GEN_ARRIVAL_REGISTER_STOCK, ctxRegister);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
    }

    /**
     * 生成物流清关费用
     * @param ctx 上下文
     */
    private void genLogistics(BizContext ctx){
        // 入参上下文
        BizReceiptDeliveryNoticeHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        if(!EnumSendType.OFFSHORE_PROCUREMENT.getValue().equals(po.getSendType())){
            // 非离岸采购不生成物流清关费用
            return;
        }

        // 构造物流清关费用单头
        BizReceiptLogisticsHeadDTO logisticsHead = UtilBean.newInstance(po, BizReceiptLogisticsHeadDTO.class);
        logisticsHead
                .setContractTaxCode(po.getContractTaxCode())
                .setId(null)
                .setReceiptCode(null)
                .setReceiptType(EnumReceiptType.LOGISTICS.getValue())  // 设置单据类型为物流清关费用
                .setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue())  // 设置状态为草稿
                .setCreateTime(new Date())
                .setModifyTime(new Date())
                .setCreateUserId(ctx.getCurrentUser().getId())
                .setModifyUserId(ctx.getCurrentUser().getId())
                .setTransportType(po.getTransportType())
                .setCanDeliveryDate(po.getCanDeliveryDate())
                .setTransportBatch(po.getTransportBatch())
                .setIsDelete(0)
                .setRemark("由送货通知单" + po.getReceiptCode() + "自动生成");

        List<BizReceiptLogisticsItemDTO> itemList = new ArrayList<>();
        List<BizReceiptLogisticsCaseRelDTO> caseRelList = new ArrayList<>();
        
        // 遍历送货通知单行项目,生成对应的物流清关费用行项目

           
        

        // 计算 所有行项目PO总价（不含税）之和
        BigDecimal totalPoAmount = po.getItemList().stream()
            .filter(item -> UtilNumber.isNotEmpty(item.getPoNoTaxAmount()))
            .map(item -> item.getPoNoTaxAmount())
            .reduce(BigDecimal.ZERO, BigDecimal::add);  

        // 计算 所有相同物资类别的行项目PO总价（不含税）之和

        /*Map<String, BigDecimal> matTypeTotalPoAmountMap = po.getItemList().stream()
        .collect(Collectors.groupingBy(BizReceiptDeliveryNoticeItemDTO::getMatTypeStr,Collectors.reducing(
            BigDecimal.ZERO,
            item -> UtilObject.isNull(item.getPoNoTaxAmount()) ? BigDecimal.ZERO : item.getPoNoTaxAmount(),
            BigDecimal::add)));*/





      
        po.getItemList().forEach(deliveryItem -> {
            BizReceiptLogisticsItemDTO logisticsItem = UtilBean.newInstance(deliveryItem, BizReceiptLogisticsItemDTO.class);
            logisticsItem.setPreReceiptHeadId(deliveryItem.getHeadId());
            logisticsItem.setPreReceiptItemId(deliveryItem.getId());
            logisticsItem.setPreReceiptType(EnumReceiptType.DELIVERY_NOTICE.getValue());
            logisticsItem.setCreateTime(new Date());



            // 分类价值百分比=行项目PO总价（不含税）*100%/所有相同物资类别的行项目PO总价（不含税）之和
            // 货物价值百分比=行项目PO总价（不含税）*100%/所有行项目PO总价（不含税）之和

            //设置 分类价值百分比
            // logisticsItem.setCategoryValueRatio(deliveryItem.getPoNoTaxAmount().multiply(BigDecimal.valueOf(100)).divide(matTypeTotalPoAmountMap.get(deliveryItem.getMatTypeStr()),2,BigDecimal.ROUND_HALF_UP));


            //设置 货物价值百分比
            logisticsItem.setCargoValueRatio(deliveryItem.getPoNoTaxAmount().multiply(BigDecimal.valueOf(100)).divide(totalPoAmount,2,BigDecimal.ROUND_HALF_UP));
            
            
            itemList.add(logisticsItem);
            
        });

        // 所有相同物资类别的行项目的分类价值百分比之和必须等于100%，大于或小于时则自动扣减或增加此物资类别的第一行的分类价值百分比,第一行不足或超过100%，则用第二行增加扣减，依次类推；

        /*Map<String,List<BizReceiptLogisticsItemDTO>> matTypeItemMap = itemList.stream()
        .collect(Collectors.groupingBy(BizReceiptLogisticsItemDTO::getMatTypeStr));*/

        /*matTypeItemMap.forEach((matType,valueList) -> {
            BigDecimal totalCategoryValueRatio = valueList.stream()
            .map(item -> item.getCategoryValueRatio())
            .reduce(BigDecimal.ZERO, BigDecimal::add);
            if(totalCategoryValueRatio.compareTo(BigDecimal.valueOf(100))!=0){
                BigDecimal diff = totalCategoryValueRatio.subtract(BigDecimal.valueOf(100));
                // 大于或小于时则自动扣减或增加此物资类别的第一行的分类价值百分比
                for(BizReceiptLogisticsItemDTO item:valueList){
                    if(item.getCategoryValueRatio().subtract(diff).compareTo(BigDecimal.ZERO)<0||item.getCategoryValueRatio().add(diff).compareTo(BigDecimal.valueOf(100))>0){
                        continue;
                    }
                    item.setCategoryValueRatio(item.getCategoryValueRatio().subtract(diff));
                    break;
                }
            }
        }); */


        // 所有行项目的货物价值百分比之和必须等于100%，大于或小于时则自动扣减或增加第一行的货物价值百分比,第一行不足或超过100%，则用第二行增加扣减，依次类推；

        BigDecimal totalCargoValueRatio = itemList.stream()
        .map(item -> item.getCargoValueRatio())
        .reduce(BigDecimal.ZERO, BigDecimal::add);  

        if(totalCargoValueRatio.compareTo(BigDecimal.valueOf(100))!=0){
            BigDecimal diff = totalCargoValueRatio.subtract(BigDecimal.valueOf(100));
            for(BizReceiptLogisticsItemDTO item:itemList){
                if(item.getCargoValueRatio().subtract(diff).compareTo(BigDecimal.ZERO)<0||item.getCargoValueRatio().add(diff).compareTo(BigDecimal.valueOf(100))>0){
                    continue;
                }
                item.setCargoValueRatio(item.getCargoValueRatio().subtract(diff));
                break;
            }   
        }   

        po.getCaseNowList().forEach(caseNow -> {
            BizReceiptLogisticsCaseRelDTO logisticsCaseRel = UtilBean.newInstance(caseNow, BizReceiptLogisticsCaseRelDTO.class);
            logisticsCaseRel.setHeadId(po.getId());
            logisticsCaseRel.setCreateTime(new Date());
            caseRelList.add(logisticsCaseRel);
        });

    
        logisticsHead.setItemList(itemList);
        logisticsHead.setCaseNowList(caseRelList);
        // 构造上下文
        BizContext logisticsCtx = new BizContext();
        logisticsCtx.setCurrentUser(ctx.getCurrentUser());
        logisticsCtx.setContextData(Const.BIZ_CONTEXT_KEY_PO, logisticsHead);

        // 调用物流清关费用组件保存方法
        logisticsService.save(logisticsCtx);
    }


    /**
     * 生成采购订单(插入数据库)
     * @param ctx
     */
    public void genPurchaseReceipt(BizContext ctx){
        BizReceiptDeliveryNoticeHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        if(EnumSendType.OFFSHORE_PROCUREMENT.getValue().equals(headDTO.getSendType())){
            // 离岸采购不生成采购订单
            return;
        }


        ErpPurchaseReceiptHead purchaseHead = new ErpPurchaseReceiptHead();
        purchaseHead.setReceiptCode(headDTO.getPurchaseCode());
        purchaseHead.setCreateTime(new Date());
        purchaseHead.setIsReturnFlag(0);
        purchaseHead.setErpCreateTime(new Date());
        purchaseHead.setErpCreateUserName(headDTO.getModifyUserName());
        purchaseHead.setPaymentMethod(headDTO.getPaymentMethod());
        purchaseHead.setCurrency(headDTO.getCurrency());
        purchaseHead.setContractCode(headDTO.getContractCode());
        purchaseHead.setContractName(headDTO.getContractName());
        purchaseHead.setSupplierCode(headDTO.getSupplierCode());
        purchaseHead.setSupplierName(headDTO.getSupplierName());
        erpPurchaseReceiptHeadDataWrap.saveDto(purchaseHead);


        List<ErpPurchaseReceiptItem> purchaseItemList = new ArrayList<>();

        for(BizReceiptDeliveryNoticeItemDTO itemDTO:headDTO.getItemList()){
            ErpPurchaseReceiptItem purchaseItem = UtilBean.newInstance(itemDTO, ErpPurchaseReceiptItem.class);
            purchaseItem.setId(null);
            purchaseItem.setHeadId(purchaseHead.getId());
            purchaseItem.setRid(itemDTO.getRid());
            purchaseItem.setTaxCode(itemDTO.getContractTaxCode());
            purchaseItem.setReceiptQty(itemDTO.getQty());
            if(headDTO.getSendType().equals(EnumSendType.INLAND_PROCUREMENT.getValue()) || headDTO.getSendType().equals(EnumSendType.OFFSHORE_PROCUREMENT.getValue())){
                // 内贸与离岸，取po单价
                purchaseItem.setPrice(itemDTO.getPoNoTaxPrice());
            }else{
                // 在岸与油品，取单价
                purchaseItem.setPrice(itemDTO.getNoTaxPrice());
            }
            purchaseItemList.add(purchaseItem);
        }

        erpPurchaseReceiptItemDataWrap.saveBatch(purchaseItemList); 

    }

    /**
     * 送货通知-单据复制
     *
     * @in ctx 入参 {@link BizReceiptDeliveryNoticeHeadDTO : "送货通知单"}
     * @out ctx 入参 {@link BizReceiptDeliveryNoticeHeadDTO : "送货通知单"}
     */
    public void copyReceipt(BizContext ctx) {
        // 入参上下文
        BizReceiptDeliveryNoticeHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser user = ctx.getCurrentUser();
        // 设置抬头
        headDTO.setId(null).setCreateTime(null);
        // 设置行项目
        BizReceiptPreSearchPO po = new BizReceiptPreSearchPO();
        po.setPurchaseReceiptCode(headDTO.getItemList().get(0).getReferReceiptCode()).setIsReturnFlag(EnumRealYn.FALSE.getIntValue());
        // 调用SAP查询采购订单
        List<ErpPurchaseReceiptItemDTO> purchaseReceiptItemVoList = purchaseReceiptService.getErpPurchaseReceiptItemList(po, user);
        headDTO.getItemList().forEach(p -> {
            purchaseReceiptItemVoList.forEach(q -> {
                if(p.getReferReceiptItemId().equals(q.getId())) {
                    // 更新可送货数量
                    p.setCanDeliveryQty(q.getCanDeliveryQty());
                }
            });
        });
        // 保存单据
        this.saveDeliveryNotice(ctx);
    }

    /**
     * 发起审批
     *
     * @in ctx 入参 {@link BizReceiptApplyHeadDTO : "申请单"}
     */
    public void startWorkFlow(BizContext ctx) {
        BizReceiptDeliveryNoticeHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);


        //离岸采购触发审批
        if(!EnumSendType.OFFSHORE_PROCUREMENT.getValue().equals(headDTO.getSendType())){
            return;
        }
     
            // 发起流程审批
        Long receiptId = headDTO.getId();
        String receiptCode = headDTO.getReceiptCode();
        Integer receiptType = headDTO.getReceiptType();
        Map<String, Object> variables = new HashMap<>();
            
        // 经营管理部
        String deptCode = EnumDept.BMD.getCode();
        List<String> userList1 = sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptCode, null, EnumApprovalLevel.LEVEL_2);
        if (UtilCollection.isEmpty(userList1)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, EnumUserJob.LEVEL_1_APPROVAL.getValue().toString());
        }
        List<String> userList2 = sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptCode, null, EnumApprovalLevel.LEVEL_3);
        if (UtilCollection.isEmpty(userList2)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, EnumUserJob.LEVEL_2_APPROVAL.getValue().toString());
        }
        variables.put("deptCode", deptCode);
        DicDept dicDept = dicDeptDataWrap.getOne(new QueryWrapper<DicDept>().lambda().eq(DicDept::getDeptCode, deptCode));
        // [31443]【离岸送货】离岸送货待办展示修改为：“请审批[XXXX公司+XX部门]创建人名称提交的华信与能殷PO批次发货订单审批流程：华信能殷PO批次审批        ；  “华信与能殷PO批次发货订单”是固定值，“华信能殷PO批次审批 ”取输入的描述
        variables.put("subject", "请审批[" + dictionaryService.getCorpCacheById(ctx.getCurrentUser().getCorpId()).getCorpName() + dicDept.getDeptName() + "]" + headDTO.getCreateUserName() + "提交的华信与能殷PO批次发货订单审批流程：" + headDTO.getDeliveryNoticeDescribe());

        List<String> userList = Arrays.asList(UtilConst.getInstance().getHxLeader(), UtilConst.getInstance().getNyLeader());

        variables.put("assigneeList", userList);
        variables.put("nrOfInstances", userList.size());
        variables.put("nrOfActiveInstances", userList.size());
        variables.put("nrOfCompletedInstances", 0); 

        variables.put("rejectedCount", 0);  // 驳回计数
        variables.put("approvedCount", 0);  // 通过计数
        
        workflowService.setBizReceiptVariables(variables, receiptId, receiptCode, receiptType, ctx, headDTO.getDeliveryNoticeDescribe());
        workflowService.startWorkFlow(receiptId, receiptCode, receiptType, variables);

        // 更新状态审批中
        this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue());
        
    }

    

    /**
     * 审批回调
     *
     * @in ctx 入参 {@link BizApprovalReceiptInstanceRelDTO ："回调参数"}
     */
    
    public void approvalCallback(BizApprovalReceiptInstanceRelDTO wfReceiptCo) {
        BizContext ctx = new BizContext();
        CurrentUser currentUser = wfReceiptCo.getInitiator();
        ctx.setCurrentUser(currentUser);
        BizReceiptDeliveryNoticeHead head = deliveryNoticeHeadDataWrap.getById(wfReceiptCo.getReceiptHeadId());
        BizReceiptDeliveryNoticeHeadDTO headDTO = UtilBean.newInstance(head, BizReceiptDeliveryNoticeHeadDTO.class);
        dataFillService.fillAttr(headDTO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
        if (wfReceiptCo.getApproveStatus().equals(EnumApprovalStatus.FINISH.getValue())) {
            if (UtilNumber.isEmpty(wfReceiptCo.getReceiptHeadId())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
            }
            // 更新状态已完成
            this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());

            // 生成到货登记单
            this.genArrivalRegister(ctx);

            // 生成物流清关费用
            this.genLogistics(ctx);

            // 生成采购订单（插入数据库）
            this.genPurchaseReceipt(ctx);
        } else {
            // 更新状态已驳回
            this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue());

            // 驳回送货通知 回写供应商箱件已发货数量
            this.writeBackSupplierCase(ctx);    

            // 驳回送货通知 回写合同已发货数量
            this.writeBackContract(ctx);
        }
    }


    public void createPurchase(BizContext ctx){

        


        BizReceiptDeliveryNoticeHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);


        if(EnumSendType.OFFSHORE_PROCUREMENT.getValue().equals(headDTO.getSendType())){
            // 离岸采购 不生成采购订单
            return;
            
        }

        this.createPurchaseSap(ctx);
            

    }

    @Autowired
    private HXSapIntegerfaceService sapIntegerfaceService;

    /**
     * 调用sap生产采购订单
     * @param ctx
     */
    public void createPurchaseSap(BizContext ctx){

        BizReceiptDeliveryNoticeHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        HXPurchaseOrderHeader header = new HXPurchaseOrderHeader();

        // 油品送货：默认ZY01
        if(headDTO.getSendType().equals(EnumSendType.OIL_PROCUREMENT.getValue())){

            header.setPurchaseOrderType(EnumPurchaseOrderType.STANDARD_PO.getCode());

        }else if(headDTO.getSendType().equals(EnumSendType.ONSHORE_PROCUREMENT.getValue())){
            // 判断 第一个行项目判断，若第一个行项目物料组是Y501资产，则BSART传ZY02，非Y501资产，在BSART传ZY01；
            if(headDTO.getItemList().get(0).getMatGroupCode().equals("Y501")){
                header.setPurchaseOrderType(EnumPurchaseOrderType.ASSET_PO.getCode());
            }else{
                header.setPurchaseOrderType(EnumPurchaseOrderType.STANDARD_PO.getCode());
            }
        }else if(headDTO.getSendType().equals(EnumSendType.INLAND_PROCUREMENT.getValue())){
            // 判断 第一个行项目判断，若第一个行项目物料组是Y501资产，则BSART传ZY02，非Y501资产，在BSART传ZY01；
            if(headDTO.getItemList().get(0).getMatGroupCode().equals("Y501")){
                header.setPurchaseOrderType(EnumPurchaseOrderType.ASSET_PO.getCode());
            }else{
                header.setPurchaseOrderType(EnumPurchaseOrderType.STANDARD_PO.getCode());
            }
        }else{
            // 其他类型 不生成采购订单
            throw new WmsException(EnumReturnMsg.RETURN_CODE_UNSUPPORTED_PURCHASE_TYPE);
        }
        header.setReferenceReceiptCode(headDTO.getReceiptCode());
        if (EnumSendType.INLAND_PROCUREMENT.getValue().equals(headDTO.getSendType())) {
            header.setSupplierCode(UtilString.isNotNullOrEmpty(headDTO.getInlandSupplierCode()) ? headDTO.getInlandSupplierCode() : headDTO.getSupplierCode());
        } else {
            header.setSupplierCode(headDTO.getSupplierCode());
        }
        // header.setPaymentTerms(EnumContractPaymentMethod.getByValue(headDTO.getPaymentMethod()).getDesc());
        header.setCurrency(EnumContractCurrency.getByValue(headDTO.getCurrency()).getDesc());  

        List<HXPurchaseOrderItem> items =new ArrayList<>();


        for(BizReceiptDeliveryNoticeItemDTO itemDTO:headDTO.getItemList()){
            HXPurchaseOrderItem item = new HXPurchaseOrderItem();
            item.setItemCode(itemDTO.getRid());
            item.setMatCode(itemDTO.getMatCode());
            if(header.getPurchaseOrderType().equals(EnumPurchaseOrderType.ASSET_PO.getCode())){
                // 资产类采购订单品名使用品名
                item.setMatName(itemDTO.getProductName());
                item.setAccountCategory("A");
            }else{
                // 生产类采购订单品名使用物料描述
                item.setMatName(itemDTO.getMatName());
                item.setAccountCategory(Const.STRING_EMPTY);
            }


            item.setMaterialGroup(itemDTO.getMatGroupCode());
            item.setQty(UtilBigDecimal.getString(itemDTO.getQty()));
            item.setUnitCode(itemDTO.getUnitCode());
            EnumContractTaxRate taxCode = EnumContractTaxRate.getByKey(itemDTO.getContractTaxCode());
            if(taxCode != null){
                item.setTaxCode(taxCode.getCode());
            }
            if(headDTO.getSendType().equals(EnumSendType.INLAND_PROCUREMENT.getValue()) || headDTO.getSendType().equals(EnumSendType.OFFSHORE_PROCUREMENT.getValue())){
                // 内贸与离岸，取po单价
                item.setNetPrice(UtilBigDecimal.getString(itemDTO.getPoNoTaxPrice()));
            }else{
                // 在岸与油品，取单价
                item.setNetPrice(UtilBigDecimal.getString(itemDTO.getNoTaxPrice()));
            }
            item.setDeliveryDate(UtilDate.convertDateToDateStr(headDTO.getCanDeliveryDate()));
            if(header.getPurchaseOrderType().equals(EnumPurchaseOrderType.ASSET_PO.getCode())){
                item.setAssetCardNumber(itemDTO.getAssetCardNo());
            }
            item.setFactory(itemDTO.getFtyCode());
            items.add(item);
        }   

        header.setItems(items);

        HXPurchaseOrderReturn erpReturnObj = sapIntegerfaceService.createPurchaseOrder(header);

        if(erpReturnObj.getSuccess().equals(Const.ERP_RETURN_TYPE_S)){

            headDTO.setPurchaseCode(erpReturnObj.getPurchaseOrderCode());

            UpdateWrapper<BizReceiptDeliveryNoticeHead> headQueryWrapper = new UpdateWrapper<>();
    
            headQueryWrapper.lambda().set(BizReceiptDeliveryNoticeHead::getPurchaseCode,headDTO.getPurchaseCode()).eq(BizReceiptDeliveryNoticeHead::getId,headDTO.getId());
    
            deliveryNoticeHeadDataWrap.update(headQueryWrapper);
    
        
            if(UtilCollection.isNotEmpty(headDTO.getItemList()) ){
                for(BizReceiptDeliveryNoticeItemDTO itemDTO:headDTO.getItemList()){
                    itemDTO.setPurchaseCode(headDTO.getPurchaseCode());
                    // 生成自动流水
                    itemDTO.setPurchaseRid(itemDTO.getRid());
                }
            }
            // 更新送货通知单行项目
            deliveryNoticeItemDataWrap.updateBatchDtoById(headDTO.getItemList());  


        }else{

            // 更新状态未同步   
            this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());

            throw new WmsException(EnumReturnMsg.RETURN_CODE_CREATE_PURCHASE_ORDER_FAILED,erpReturnObj.getReturnMessage());
        }

    }

    public void autoApproval(BizContext ctx){
        BizReceiptDeliveryNoticeHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);


        // if (EnumSendType.OFFSHORE_PROCUREMENT.getValue().equals(headDTO.getSendType())) {
        //     // 非离岸采购自动审批完成
        //     return;
        // }

        BizApprovalReceiptInstanceRelDTO wfReceiptCo = new BizApprovalReceiptInstanceRelDTO();
        wfReceiptCo.setReceiptHeadId(headDTO.getId());
        wfReceiptCo.setApproveStatus(EnumApprovalStatus.FINISH.getValue());
        wfReceiptCo.setInitiator(ctx.getCurrentUser());
        this.approvalCallback(wfReceiptCo);
    }


    /**
     * 回写合同已发货数量
     * @param ctx
     */
    public void writeBackContract(BizContext ctx){
        BizReceiptDeliveryNoticeHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        Map<Long, BizReceiptContractItemQtyDTO> contractItemMap = new HashMap<>();

        if(UtilCollection.isNotEmpty(headDTO.getItemList())){
            for(BizReceiptDeliveryNoticeItemDTO itemDTO:headDTO.getItemList()){
                Long key = itemDTO.getPreReceiptItemId();
                if(EnumReceiptType.CONTRACT_RECEIPT.getValue().equals(itemDTO.getPreReceiptType())){
                    if(contractItemMap.containsKey(key)){
                        BizReceiptContractItemQtyDTO contractItem = contractItemMap.get(key);
                        contractItem.setSendQty(contractItem.getSendQty().add(itemDTO.getQty()));
                    }else {
                        BizReceiptContractItemQtyDTO contractItem = new BizReceiptContractItemQtyDTO();
                        contractItem.setId(itemDTO.getPreReceiptItemId());
                        contractItem.setSendQty(itemDTO.getQty());
                        contractItemMap.put(key,contractItem);
                    }
                }
            }
        }

       

        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);

        // 提交时回写增加 驳回时回写扣减    
        if(EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT.equals(operationLogType)){

        }else{
            // 驳回时回写扣减 数量取相反数
            contractItemMap.values().forEach(item -> item.setSendQty(item.getSendQty().negate()));    
        }
        contractItemMap.values().forEach(item -> {
            if(headDTO.getSendType().equals(EnumSendType.OIL_PROCUREMENT.getValue())){
                item.setFrameworkContract(true);
            }
        });  
        if(UtilCollection.isNotEmpty(contractItemMap.values())){
            BizContext newCtx = new BizContext();  
            newCtx.setContextData(Const.BIZ_CONTEXT_KEY_LIST,new ArrayList<>(contractItemMap.values()));
            newCtx.setCurrentUser(ctx.getCurrentUser());
            contractComponent.batchUpdateItemQty(newCtx);
        }
       
    }


    /**
     * 关闭送货通知时回写合同已发货数量
     * 回写数量为  发货数量-已入库数量
     * @param ctx
     */
    public void writeBackContractByClosed(BizContext ctx){
        BizReceiptDeliveryNoticeHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        Map<Long, BizReceiptContractItemQtyDTO> contractItemMap = new HashMap<>();

        if(UtilCollection.isNotEmpty(headDTO.getItemList())){
            for(BizReceiptDeliveryNoticeItemDTO itemDTO:headDTO.getItemList()){
                Long key = itemDTO.getReferReceiptItemId();
                if(contractItemMap.containsKey(key)){
                    BizReceiptContractItemQtyDTO contractItem = contractItemMap.get(key);
                    contractItem.setSendQty(contractItem.getSendQty().add(itemDTO.getQty().subtract(itemDTO.getInputQty())));
                }else {
                    BizReceiptContractItemQtyDTO contractItem = new BizReceiptContractItemQtyDTO();
                    contractItem.setId(itemDTO.getReferReceiptItemId());
                    contractItem.setSendQty(itemDTO.getQty().subtract(itemDTO.getInputQty()));
                    contractItemMap.put(key,contractItem);
                }
            }
        }

       

        
        // 驳回时回写扣减 数量取相反数
        contractItemMap.values().forEach(item -> item.setSendQty(item.getSendQty().negate()));    
        
        contractItemMap.values().forEach(item -> {
            if(headDTO.getSendType().equals(EnumSendType.OIL_PROCUREMENT.getValue())){
                item.setFrameworkContract(true);
            }
        });  
        if(UtilCollection.isNotEmpty(contractItemMap.values())){
            BizContext newCtx = new BizContext();  
            newCtx.setContextData(Const.BIZ_CONTEXT_KEY_LIST,new ArrayList<>(contractItemMap.values()));
            newCtx.setCurrentUser(ctx.getCurrentUser());
            contractComponent.batchUpdateItemQty(newCtx);
        }
       
    }


    /**
     * 回写供应商箱件已发货数量
     * @param ctx
     */
    public void writeBackSupplierCase(BizContext ctx){
        BizReceiptDeliveryNoticeHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);


        Integer isDelivery = 0;

        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);

        // 提交时回写增加 驳回时回写扣减    
        if(EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT.equals(operationLogType)){
            isDelivery = 1;
        }else{
            isDelivery = 0;
        }


        if(UtilCollection.isNotEmpty(headDTO.getCaseNowList())){
            
            List<BizReceiptSupplierCaseRelDTO> caseList = new ArrayList<>();
            for(BizReceiptDeliveryNoticeCaseRelDTO caseDTO:headDTO.getCaseNowList()){
                BizReceiptSupplierCaseRelDTO caseRelDTO = new BizReceiptSupplierCaseRelDTO();
                caseRelDTO.setId(caseDTO.getPreReceiptItemId());
                caseRelDTO.setIsDelivery(isDelivery);
                caseList.add(caseRelDTO);
            }
            supplierCaseRelDataWrap.updateBatchDtoById(caseList);
        }

    }

    /**
     * 校验用户的工厂权限
     *
     * @param currentUser currentUser
     * @return
     */
    public Boolean checkUserFactory(CurrentUser currentUser) {
        // 用户只有J358工厂权限
        if (currentUser.getFactoryList().stream().allMatch(factory -> "J358".equals(factory.getFtyCode()))) {
            return false;
        }
        // 用户有J046工厂权限
        if (currentUser.getFactoryList().stream().anyMatch(factory -> "J046".equals(factory.getFtyCode()))) {
            return true;
        }
        return false;
    }

    /**
     * 开启审批
     *
     * @in ctx 入参 {@link BizResultVO (head":"采购验收","extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO (head":"采购验收及单审批信息","extend":"扩展功能开启审批")}
     */
    public void setExtendWf(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptDeliveryNoticeHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        // // 判断业务流程是否需要审批
        boolean wfByReceiptType = UtilConst.getInstance().getWfByReceiptType(EnumReceiptType.DELIVERY_NOTICE.getValue());
        
        
        
        if (UtilObject.isNull(resultVO.getHead())) {
            // 初始化 - 设置审批开启/关闭
            resultVO.getExtend().setWfRequired(false);
        } else {
            // 详情页 - 设置审批开启/关闭
            if (wfByReceiptType) {

                if(!resultVO.getHead().getSendType().equals(EnumSendType.OFFSHORE_PROCUREMENT.getValue())){
                    // 非离岸采购送货不开启审批
                    resultVO.getExtend().setWfRequired(false);
                    return;
                }else{
                    resultVO.getExtend().setWfRequired(wfByReceiptType);
                }

                resultVO.getExtend().setWfRequired(wfByReceiptType);
                if (UtilObject.isNotNull(resultVO.getHead())) {
                    List<BizApproveRecordDTO> approveList = approvalService.getHistoryActivity(resultVO.getHead().getReceiptCode());
                    resultVO.getHead().setApproveList(approveList);
                    if (UtilCollection.isNotEmpty(approveList)) {
                        resultVO.getHead().setProInstanceId(Long.valueOf(approveList.get(approveList.size() - 1).getProInstanceId()));
                    }
                }
            }
        }
        // 设置审批详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }


    public void importCase(BizContext ctx) {
        //获取Excel附件
        MultipartFile file = ctx.getContextData(Const.BIZ_CONTEXT_KEY_FILE);

        try {
            //获取EXCEL数据
            List<BizReceiptDeliveryNoticeCaseRelImport> list = (List<BizReceiptDeliveryNoticeCaseRelImport>) UtilExcel.readExcelData(file.getInputStream(), BizReceiptDeliveryNoticeCaseRelImport.class);
            if (UtilCollection.isEmpty(list)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_IMOPRT_TEMPLATE_HAS_NO_DATA);
            }
            List<String> packTypeList = list.stream().map(obj -> obj.getPackageType() == null ? "" : obj.getPackageType().trim()).distinct().collect(Collectors.toList());
            for (String s : packTypeList) {
                if (!s.equals("纸箱")&&!s.equals("裸装")&&!s.equals("木箱")&&!s.equals("罐车")&&!s.equals("钢瓶")&&!s.equals("托盘")&&!s.equals("袋装")&&!s.equals("其他")){
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_UNSUP_PACK,s);
                }
            }
            List<BizReceiptDeliveryNoticeCaseRelDTO> bizReceiptDeliveryNoticeCaseRelDTOS = UtilCollection.toList(list, BizReceiptDeliveryNoticeCaseRelDTO.class);
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO,bizReceiptDeliveryNoticeCaseRelDTOS);
        } catch (IOException e) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_IO_EXCEPTION);
        }
    }



    /**
     * 查询合同列表
     * @param ctx
     */
    public void getContract(BizContext ctx){

        BizReceiptContractSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        if(UtilNumber.isEmpty(po.getSendType())){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);   
        }   

        if(po.getSendType()==1||po.getSendType()==4){
            // 离岸采购或内贸   
            po.setContractSubTypeList(Arrays.asList(4,5));
        }
        if(po.getSendType()==2){
            // 在岸
            po.setContractSubTypeList(Arrays.asList(1));

            this.setAuth(ctx);
            
        }
        if(po.getSendType()==3){
            // 油品
            po.setContractSubTypeList(Arrays.asList(4,5));

            this.setAuth(ctx);
        }

        // 查询执行中的合同
        po.setReceiptStatusList(Arrays.asList(EnumReceiptStatus.RECEIPT_STATUS_EXECUTING.getValue()));

        QueryWrapper<BizReceiptContractHead> headQueryWrapper = new QueryWrapper<>();
        headQueryWrapper.lambda()
                .like(UtilString.isNotNullOrEmpty(po.getContractCode()),BizReceiptContractHead::getReceiptCode,po.getContractCode())
                .like(UtilString.isNotNullOrEmpty(po.getContractName()),BizReceiptContractHead::getContractName,po.getContractName())
                .like(UtilString.isNotNullOrEmpty(po.getReceiptCode()),BizReceiptContractHead::getReceiptCode,po.getReceiptCode())
                .in(UtilCollection.isNotEmpty(po.getContractSubTypeList()),BizReceiptContractHead::getContractSubType,po.getContractSubTypeList())
                .in(UtilCollection.isNotEmpty(po.getReceiptStatusList()),BizReceiptContractHead::getReceiptStatus,po.getReceiptStatusList())
                .eq(UtilNumber.isNotEmpty(po.getSupplierId()),BizReceiptContractHead::getSupplierId,po.getSupplierId())
                ;

        List<BizReceiptContractHead> headList = bizReceiptContractHeadDataWrap.list(headQueryWrapper);

        List<BizReceiptDeliveryNoticeHeadDTO> vo2 = new ArrayList<>();

        for(BizReceiptContractHead head : headList){
            BizReceiptDeliveryNoticeHeadDTO dto = UtilBean.newInstance(head, BizReceiptDeliveryNoticeHeadDTO.class);
            dto.setContractCode(head.getReceiptCode());
            dto.setId(head.getId());
            dto.setContractId(head.getId());
            vo2.add(dto);
        }

        dataFillService.fillAttr(vo2);

        // B10660 内贸送货 供应商支持手动修改 默认为合同带出来的供应商
        if (EnumSendType.INLAND_PROCUREMENT.getValue().equals(po.getSendType())) {
            for (BizReceiptDeliveryNoticeHeadDTO dto : vo2) {
                dto.setInlandSupplierId(dto.getSupplierId());
                dto.setInlandSupplierCode(dto.getSupplierCode());
                dto.setInlandSupplierName(dto.getSupplierName());
            }
        }

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO,vo2);
    }

    private void setAuth(BizContext ctx){
        BizReceiptContractSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        // JS01，JS03，JS04的角色 可以查询所有供应商合同    
        // 当前登录人
        CurrentUser user = ctx.getCurrentUser(); 

        Set<String> userRoleSet = user.getSysUserRoleRelList().stream().map(SysUserRoleRel::getRoleCode).collect(Collectors.toSet());

        Set<String> roleSet = new HashSet<>();
        roleSet.add("JS01");
        roleSet.add("JS03");
        roleSet.add("JS04");

        // 取交集
        userRoleSet.retainAll(roleSet);

        if(!userRoleSet.isEmpty()){
                // JS01，JS03，JS04的角色 可以查询所有供应商合同
            po.setSupplierId(null);
        }else{
            // 供应商只能查询自己供应商合同
            List<DicSupplierUserRel> supplierUserRelList = bidderUserRelDataWrap.list(new QueryWrapper<DicSupplierUserRel>() {{
                    lambda().eq(DicSupplierUserRel::getUserId, user.getId());
                }});
            if (UtilCollection.isNotEmpty(supplierUserRelList)) {
                po.setSupplierId(supplierUserRelList.get(0).getSupplierId());
            }else{
                po.setSupplierId(-1L);
            }
        }
    }

    /**
     * 查询合同明细
     * @param ctx
     */
    public void getContractInfo(BizContext ctx){
        Long id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        Integer sendType = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        BizReceiptContractHead contractHead = bizReceiptContractHeadDataWrap.getById(id);
        BizReceiptContractHeadDTO contractHeadDTO = UtilBean.newInstance(contractHead,BizReceiptContractHeadDTO.class);
        dataFillService.fillAttr(contractHeadDTO);

        BizReceiptDeliveryNoticeHeadDTO deliveryNoticeHeadDTO = new BizReceiptDeliveryNoticeHeadDTO();


        // 设置默认仓库
        Long locationId = dictionaryService.getLocationIdCacheByCode(Const.DEFAULT_FACTORY_CODE,Const.DEFAULT_LOCATION_CODE);

        DicStockLocationDTO locationDTO = dictionaryService.getLocationCacheById(locationId);

        // 油品送货的库存地点默认为J01库存地点
        if(sendType!=null&&sendType==3){
            locationId = dictionaryService.getLocationIdCacheByCode(Const.DEFAULT_FACTORY_CODE,Const.DEFAULT_LOCATION_CODE_J01);
            locationDTO = dictionaryService.getLocationCacheById(locationId);

        }

        List<BizReceiptDeliveryNoticeItemDTO> itemDTOList = new ArrayList<>();

        if(UtilCollection.isEmpty(contractHeadDTO.getItemList())){
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO,deliveryNoticeHeadDTO);
            return;
        }
        for(BizReceiptContractItemDTO contractItemDTO:contractHeadDTO.getItemList() ){
            BizReceiptDeliveryNoticeItemDTO itemInfo = UtilBean.newInstance(contractItemDTO,BizReceiptDeliveryNoticeItemDTO.class);

            itemInfo.setId(null);
            itemInfo.setHeadId(null);
            itemInfo.setRid(Const.STRING_EMPTY);
            itemInfo.setReceiptCode(Const.STRING_EMPTY);
            itemInfo.setPreReceiptHeadId(contractItemDTO.getHeadId());
            itemInfo.setPreReceiptItemId(contractItemDTO.getId());
            itemInfo.setContractCode(contractHeadDTO.getReceiptCode());
            itemInfo.setContractRid(contractItemDTO.getRid());
            itemInfo.setPreReceiptType(EnumReceiptType.CONTRACT_RECEIPT.getValue());
            itemInfo.setPreReceiptQty(contractItemDTO.getQty());
            itemInfo.setCanDeliveryQty(contractItemDTO.getQty().subtract(contractItemDTO.getSendQty()));
            // 不设置油品送货数量
            if(sendType!=null&&sendType==3){
                itemInfo.setQty(BigDecimal.ZERO);   
            }else{
                itemInfo.setQty(itemInfo.getCanDeliveryQty());
            }
            itemInfo.setContractTaxCode(contractItemDTO.getTaxCode());
            itemInfo.setTaxPrice(itemInfo.getTaxPrice());
            itemInfo.setTaxAmount(itemInfo.getQty().multiply(itemInfo.getTaxPrice()));
            itemInfo.setReferReceiptHeadId(contractItemDTO.getHeadId());
            itemInfo.setReferReceiptItemId(contractItemDTO.getId());
            itemInfo.setReferReceiptType(EnumReceiptType.CONTRACT_RECEIPT.getValue());
            itemInfo.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue());
            itemInfo.setDemandPerson(contractItemDTO.getDemandPersonName());
            itemInfo.setDemandDept(contractItemDTO.getDemandDeptName());
            itemInfo.setFtyId(UtilObject.isNotNull(locationDTO)
                    ? locationDTO.getFtyId() : 0L);
            itemInfo.setLocationId(UtilObject.isNotNull(locationDTO)
                    ? locationDTO.getId() : 0L);
            itemInfo.setWhId(UtilObject.isNotNull(locationDTO)
                    ? locationDTO.getWhId() : 0L);
            itemDTOList.add(itemInfo);
        }
        deliveryNoticeHeadDTO.setItemList(itemDTOList);
        dataFillService.fillAttr(deliveryNoticeHeadDTO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO,deliveryNoticeHeadDTO);
    }




    /**
     * 基于供应商箱件创建送货通知
     */
    public void getSupplierCaseList(BizContext ctx) {
        // 获取查询条件
        BizReceiptSupplierCaseSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        List<Integer> receiptStatusList = Arrays.asList(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());

        po.setReceiptStatusList(receiptStatusList);

        po.setIsDelivery(0);
       
       
        WmsQueryWrapper<BizReceiptSupplierCaseSearchPO> wrapper = supplierCaseComponent.setQueryWrapper(ctx);


        // 获取供应商箱件列表
        List<BizReceiptSupplierCaseListVo> list = supplierCaseHeadDataWrap.getSupplierCaseList(null, wrapper);
        
        // 转换为HeadDTO对象
        List<BizReceiptSupplierCaseHeadDTO> supplierCaseDTOList = UtilCollection.toList(list, BizReceiptSupplierCaseHeadDTO.class);
        // 填充关联属性
        dataFillService.fillAttr(supplierCaseDTOList);

        List<BizReceiptDeliveryNoticeHeadDTO> resultList = new ArrayList<>();

        // 循环转换每个供应商箱件DTO
        for (BizReceiptSupplierCaseHeadDTO supplierCaseDTO : supplierCaseDTOList) {
            BizReceiptDeliveryNoticeHeadDTO deliveryNoticeHeadDTO = new BizReceiptDeliveryNoticeHeadDTO();
            supplierCaseDTO.setCaseList(supplierCaseDTO.getCaseList().stream().filter(c -> c.getIsDelivery() == 0).collect(Collectors.toList()));

            // 设置基本信息
            deliveryNoticeHeadDTO.setReceiptCode(supplierCaseDTO.getReceiptCode())
                                .setCreateTime(supplierCaseDTO.getCreateTime());
                         

            // 将caseList转换为caseNowList
            if (UtilCollection.isNotEmpty(supplierCaseDTO.getCaseList())) {
                List<BizReceiptDeliveryNoticeCaseRelDTO> caseNowList = UtilCollection.toList(
                    supplierCaseDTO.getCaseList(), 
                    BizReceiptDeliveryNoticeCaseRelDTO.class
                );
                deliveryNoticeHeadDTO.setCaseNowList(caseNowList);
                for(BizReceiptDeliveryNoticeCaseRelDTO caseRelDTO:caseNowList){
                    
                    caseRelDTO.setRid(Const.STRING_EMPTY);
                    caseRelDTO.setPreReceiptItemId(caseRelDTO.getId());
                    caseRelDTO.setPreReceiptHeadId(caseRelDTO.getHeadId());
                    caseRelDTO.setId(null);
                    caseRelDTO.setHeadId(null);
                }

            }


            resultList.add(deliveryNoticeHeadDTO);
        }

        // 设置返回结果
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultList);
    }

    /**
     * 基于箱件编码查询物资明细
     */
    public void getSupplierCaseInfo(BizContext ctx) {
        // 获取箱件编码列表
        // 改为传的是biz_receipt_supplier_case_rel 的id
        List<Long> headIdList = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        
        if (UtilCollection.isEmpty(headIdList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        List<BizReceiptSupplierCaseRel> bizReceiptSupplierCaseRels = bizReceiptSupplierCaseRelDataWrap.listByIds(headIdList);
        List<BizReceiptSupplierCaseItem> itemList = new ArrayList<>();
        for (BizReceiptSupplierCaseRel bizReceiptSupplierCaseRel : bizReceiptSupplierCaseRels) {
            QueryWrapper<BizReceiptSupplierCaseItem> wrapper = new QueryWrapper<>();
            wrapper.lambda()
                    .eq(BizReceiptSupplierCaseItem::getHeadId, bizReceiptSupplierCaseRel.getHeadId())
                    .eq(BizReceiptSupplierCaseItem::getCaseCode, bizReceiptSupplierCaseRel.getCaseCode());
            itemList.addAll(supplierCaseItemDataWrap.list(wrapper));
        }
        
        // 转换为ItemDTO
        List<BizReceiptSupplierCaseItemDTO> itemDTOList = UtilCollection.toList(itemList, BizReceiptSupplierCaseItemDTO.class);
        // 填充关联属性
        dataFillService.fillAttr(itemDTOList);

        // 设置默认仓库
        Long locationId = dictionaryService.getLocationIdCacheByCode(Const.DEFAULT_FACTORY_CODE, Const.DEFAULT_LOCATION_CODE);
        DicStockLocationDTO locationDTO = dictionaryService.getLocationCacheById(locationId);

        // 转换为送货通知行项目
        List<BizReceiptDeliveryNoticeItemDTO> noticeItemList = new ArrayList<>();

        for (BizReceiptSupplierCaseItemDTO itemDTO : itemDTOList) {
            BizReceiptDeliveryNoticeItemDTO itemInfo = UtilBean.newInstance(itemDTO,BizReceiptDeliveryNoticeItemDTO.class);

            itemInfo.setId(null);
            itemInfo.setHeadId(null);
            itemInfo.setRid(Const.STRING_EMPTY);
            itemInfo.setReceiptCode(Const.STRING_EMPTY);
            itemInfo.setPreReceiptHeadId(itemDTO.getHeadId());
            itemInfo.setPreReceiptItemId(itemDTO.getId());
            itemInfo.setContractCode(itemDTO.getContractCode());
            itemInfo.setContractRid(itemDTO.getRid());
            itemInfo.setPreReceiptType(EnumReceiptType.SUPPLIER_CASE.getValue());
            itemInfo.setPreReceiptQty(itemDTO.getQty());
            itemInfo.setCanDeliveryQty(itemDTO.getQty());
            itemInfo.setQty(itemInfo.getCanDeliveryQty());
            itemInfo.setTaxPrice(itemInfo.getTaxPrice());
            itemInfo.setTaxAmount(itemInfo.getQty().multiply(itemInfo.getTaxPrice()).setScale(2, RoundingMode.HALF_UP));
            itemInfo.setNoTaxAmount(itemInfo.getQty().multiply(itemInfo.getNoTaxPrice()).setScale(2, RoundingMode.HALF_UP));
            itemInfo.setReferReceiptHeadId(itemDTO.getPreReceiptHeadId());
            itemInfo.setReferReceiptItemId(itemDTO.getPreReceiptItemId());
            itemInfo.setReferReceiptType(EnumReceiptType.CONTRACT_RECEIPT.getValue());
            itemInfo.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue());

            itemInfo.setFtyId(UtilObject.isNotNull(locationDTO)
                    ? locationDTO.getFtyId() : 0L);
            itemInfo.setLocationId(UtilObject.isNotNull(locationDTO)
                    ? locationDTO.getId() : 0L);
            itemInfo.setWhId(UtilObject.isNotNull(locationDTO)
                    ? locationDTO.getWhId() : 0L);
            
            
            noticeItemList.add(itemInfo);
        }
        dataFillService.fillAttr(noticeItemList);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, noticeItemList);
    }

    public void importItem(BizContext ctx) {
        MultipartFile file = ctx.getContextData(Const.BIZ_CONTEXT_KEY_FILE);

        try {
            List<BizReceiptDeliveryNoticeItemImport> importList = (List<BizReceiptDeliveryNoticeItemImport>) UtilExcel.readExcelData(file.getInputStream(), BizReceiptDeliveryNoticeItemImport.class);
            if (UtilCollection.isEmpty(importList)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_IMOPRT_TEMPLATE_HAS_NO_DATA);
            }

            // 转换为送货通知行项目
            List<BizReceiptDeliveryNoticeItemDTO> noticeItemList = UtilCollection.toList(importList, BizReceiptDeliveryNoticeItemDTO.class);
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, noticeItemList);

        } catch (IOException e) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_IO_EXCEPTION);
        }
    }

    public void importOffshoreItem(BizContext ctx) {
        MultipartFile file = ctx.getContextData(Const.BIZ_CONTEXT_KEY_FILE);

        try {
            List<BizReceiptDeliveryNoticeItemOffshoreImport> importList = (List<BizReceiptDeliveryNoticeItemOffshoreImport>) UtilExcel.readExcelData(file.getInputStream(), BizReceiptDeliveryNoticeItemOffshoreImport.class);
            if (UtilCollection.isEmpty(importList)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_IMOPRT_TEMPLATE_HAS_NO_DATA);
            }

            // 转换为送货通知行项目
            List<BizReceiptDeliveryNoticeItemDTO> noticeItemList = UtilCollection.toList(importList, BizReceiptDeliveryNoticeItemDTO.class);
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, noticeItemList);

        } catch (IOException e) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_IO_EXCEPTION);
        }
    }

    @Autowired
    private BizReceiptRegisterHeadDataWrap registerHeadDataWrap;

    @Autowired
    private BizReceiptRegisterItemDataWrap registerItemDataWrap;



    /**
     * 关闭送货通知
     * @param headId 送货通知ID
     */
    public void checkDeliveryNoticeCanClosed(BizContext ctx) {
        BizReceiptDeliveryNoticeHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        Long headId = headDTO.getId();

        // 1. 查询送货通知
        BizReceiptDeliveryNoticeHead deliveryHead = deliveryNoticeHeadDataWrap.getById(headId);
        if (deliveryHead == null) {
            throw new WmsException(EnumReturnMsg.RETURN_ERROR_DELIVERY_NOTICE_NOT_EXISTS);
        }

        // 2. 校验是否为离岸采购
        if (EnumSendType.OFFSHORE_PROCUREMENT.getValue().equals(deliveryHead.getSendType())) {
            throw new WmsException(EnumReturnMsg.RETURN_ERROR_OFFSHORE_DELIVERY_CANNOT_CLOSE);
        }

        // 3. 查询送货通知行项目ID
        List<BizReceiptDeliveryNoticeItem> deliveryItems = deliveryNoticeItemDataWrap.list(
            new LambdaQueryWrapper<BizReceiptDeliveryNoticeItem>()
                .eq(BizReceiptDeliveryNoticeItem::getHeadId, headId));

        if (UtilCollection.isEmpty(deliveryItems)) {
            return;
        }

        // 校验采购单是否可以关闭   
        logisticsComponent.checkPurchaseCanClosed(headDTO.getPurchaseCode());

    
    }

    /**
     * 删除采购订单 
     */
    public void deletePurchase(BizContext ctx){
        BizReceiptDeliveryNoticeHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 调用删除采购订单的接口 

        // 根据采购订单号 查询入库单行项目（排除 草稿状态）
        List<BizReceiptInputItem> inputItems = inputItemDataWrap.list(
            new LambdaQueryWrapper<BizReceiptInputItem>()
                .eq(BizReceiptInputItem::getPurchaseCode, headDTO.getPurchaseCode())
                .notIn(BizReceiptInputItem::getItemStatus, Arrays.asList(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue(),EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue())));

        Map<String,String> inputItemMap = inputItems.stream().collect(Collectors.toMap(e->e.getPurchaseCode()+"_"+e.getPurchaseRid(), e->e.getPurchaseCode()+"_"+e.getPurchaseRid(),(k1,k2)->k1));


        HXPurchaseOrderDeleteHeader header = new HXPurchaseOrderDeleteHeader();
        header.setPurchaseOrderCode(headDTO.getPurchaseCode());
        List<HXPurchaseOrderDeleteItem> items = new ArrayList<>();
        for(BizReceiptDeliveryNoticeItemDTO itemDTO:headDTO.getItemList()){
            HXPurchaseOrderDeleteItem item = new HXPurchaseOrderDeleteItem();
            item.setItemCode(itemDTO.getRid());

            String key = inputItemMap.get(itemDTO.getPurchaseCode()+"_"+itemDTO.getPurchaseRid());
            if(UtilString.isNotNullOrEmpty(key)){
                // 已入库
                item.setDeleteFlag("");
                item.setReceiptCompletedFlag("X");
            }else{
                // 未入库
                item.setDeleteFlag("L");
                item.setReceiptCompletedFlag("");
                item.setFtyCode(dictionaryService.getFtyCacheById(itemDTO.getFtyId()).getFtyCode());
            }
            items.add(item);
        }   

        header.setItems(items);

        HXPurchaseOrderReturn erpReturnObj = sapIntegerfaceService.deletePurchaseOrder(header);


        if(erpReturnObj.getSuccess().equals(Const.ERP_RETURN_TYPE_S)){

        }else{
            throw new WmsException(EnumReturnMsg.RETURN_CODE_CLOSE_PURCHASE_ORDER_FAILED,erpReturnObj.getReturnMessage());
        }
    }


    /**
     * 撤销送货通知
     */
    public void revokeDeliveryNotice(BizContext ctx) {
        // 1. 查询送货通知
        BizReceiptDeliveryNoticeHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        Long headId = headDTO.getId();
        BizReceiptDeliveryNoticeHead deliveryHead = deliveryNoticeHeadDataWrap.getById(headId);
        if (deliveryHead == null) {
            throw new WmsException(EnumReturnMsg.RETURN_ERROR_DELIVERY_NOTICE_NOT_EXISTS);
        }
        // 2. 校验是否为离岸采购/内贸
        if (!EnumSendType.OFFSHORE_PROCUREMENT.getValue().equals(deliveryHead.getSendType())&&!EnumSendType.INLAND_PROCUREMENT.getValue().equals(deliveryHead.getSendType())) {
            throw new WmsException(EnumReturnMsg.RETURN_ERROR_ONLY_OFFSHORE_CAN_REVOKE);
        }
        if (EnumSendType.INLAND_PROCUREMENT.getValue().equals(deliveryHead.getSendType())) {
            // [30563]【内贸送货】打开已完成的内贸通知单，请添加：撤销按钮。 先删除采购订单
            this.checkDeliveryNoticeCanClosed(ctx);
            // 调用接口删除采购订单
            this.deletePurchase(ctx);
            // 删除采购订单
            ErpPurchaseReceiptHead purchaseReceiptHead = erpPurchaseReceiptHeadDataWrap.getOne(new LambdaQueryWrapper<ErpPurchaseReceiptHead>().eq(ErpPurchaseReceiptHead::getReceiptCode, headDTO.getPurchaseCode()));
            if (UtilObject.isNotNull(purchaseReceiptHead)) {
                erpPurchaseReceiptHeadDataWrap.removeById(purchaseReceiptHead.getId());
                erpPurchaseReceiptItemDataWrap.remove(new LambdaQueryWrapper<ErpPurchaseReceiptItem>().eq(ErpPurchaseReceiptItem::getHeadId, purchaseReceiptHead.getId()));
            }
        } else



        // 送货通知为审批中状态时，撤销工作流
        if (EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue().equals(deliveryHead.getReceiptStatus())) {


            // 删除待办:必须在审批撤销前
            String id = workflowService.deleteTodo(headId);
            // 审批撤销
            RevokeDTO revokeDTO = new RevokeDTO();
            revokeDTO.setProcessInstanceId(id);
            workflowService.revoke(revokeDTO);
            // 更新送货通知状态为草稿
            this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
            return;
        }

        // 3. 查询送货通知行项目
        List<BizReceiptDeliveryNoticeItem> deliveryItems = deliveryNoticeItemDataWrap.list(
            new LambdaQueryWrapper<BizReceiptDeliveryNoticeItem>()
                .eq(BizReceiptDeliveryNoticeItem::getHeadId, headId));

        if (UtilCollection.isEmpty(deliveryItems)) {
            throw new WmsException(EnumReturnMsg.DELIVERY_NOTICE_NO_ITEMS);
        }

        // 4. 查询并校验到货登记
        List<BizReceiptRegisterItem> registerItems = registerItemDataWrap.list(
            new LambdaQueryWrapper<BizReceiptRegisterItem>()
                .eq(BizReceiptRegisterItem::getIsDelete, 0)
                .eq(BizReceiptRegisterItem::getPreReceiptHeadId, headId));

        if (UtilCollection.isNotEmpty(registerItems)) {
            // 获取到货登记抬头ID
            List<Long> registerHeadIds = registerItems.stream()
                .map(BizReceiptRegisterItem::getHeadId)
                .distinct()
                .collect(Collectors.toList());

            // 查询到货登记抬头
            List<BizReceiptRegisterHead> registerHeads = registerHeadDataWrap.listByIds(registerHeadIds);

            // 校验到货登记状态
            for (BizReceiptRegisterHead registerHead : registerHeads) {
                if (!EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(registerHead.getReceiptStatus())) {
                    throw new WmsException(EnumReturnMsg.RETURN_ERROR_REGISTER_STATUS_NOT_DRAFT);
                }
            }

            // 删除到货登记
            registerItemDataWrap.update(
                new UpdateWrapper<BizReceiptRegisterItem>()
                    .lambda()
                    .set(BizReceiptRegisterItem::getIsDelete, 1)
                    .in(BizReceiptRegisterItem::getHeadId, registerHeadIds));

            registerHeadDataWrap.update(
                new UpdateWrapper<BizReceiptRegisterHead>()
                    .lambda()
                    .set(BizReceiptRegisterHead::getIsDelete, 1)
                    .in(BizReceiptRegisterHead::getId, registerHeadIds));

            // 删除单据流
            receiptRelationService.deleteReceiptTree(registerHeadIds,EnumReceiptType.ARRIVAL_REGISTER.getValue());
        }

        // 5. 查询并处理物流清关费用
        List<BizReceiptLogisticsItem> logisticsItems = logisticsItemDataWrap.list(
            new LambdaQueryWrapper<BizReceiptLogisticsItem>()
                .eq(BizReceiptLogisticsItem::getPreReceiptHeadId, headId));

        if (UtilCollection.isNotEmpty(logisticsItems)) {
            // 校验物流清关费用状态
            for (BizReceiptLogisticsItem logisticsItem : logisticsItems) {
                if (!EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(logisticsItem.getItemStatus()) 
                    && !EnumReceiptStatus.RECEIPT_STATUS_CLOSED.getValue().equals(logisticsItem.getItemStatus())) {
                    throw new WmsException(EnumReturnMsg.RETURN_ERROR_LOGISTICS_STATUS_INVALID);
                }
            }

            // 删除草稿状态的物流清关费用
            List<Long> draftLogisticsHeadIds = logisticsItems.stream()
                .filter(item -> EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(item.getItemStatus()))
                .map(BizReceiptLogisticsItem::getHeadId)
                .distinct()
                .collect(Collectors.toList());

            if (UtilCollection.isNotEmpty(draftLogisticsHeadIds)) {


                logisticsHeadDataWrap.update(
                    new UpdateWrapper<BizReceiptLogisticsHead>()
                        .lambda()
                        .set(BizReceiptLogisticsHead::getIsDelete, 1)
                        .in(BizReceiptLogisticsHead::getId, draftLogisticsHeadIds));

                logisticsItemDataWrap.update(
                    new UpdateWrapper<BizReceiptLogisticsItem>()
                        .lambda()
                        .set(BizReceiptLogisticsItem::getIsDelete, 1)
                        .in(BizReceiptLogisticsItem::getHeadId, draftLogisticsHeadIds));

                // 删除单据流
                receiptRelationService.deleteReceiptTree(draftLogisticsHeadIds,EnumReceiptType.LOGISTICS.getValue());
            }
        }

        // B10470、B10471
        if (EnumSendType.OFFSHORE_PROCUREMENT.getValue().equals(headDTO.getSendType())
                || EnumSendType.INLAND_PROCUREMENT.getValue().equals(headDTO.getSendType())) {
            // 6. 查询并校验付款计划
            List<BizReceiptPaymentPlanItem> paymentPlanItemList = bizReceiptPaymentPlanItemDataWrap.list(new LambdaQueryWrapper<BizReceiptPaymentPlanItem>().eq(BizReceiptPaymentPlanItem::getPreReceiptHeadId, headId));

            if (UtilCollection.isNotEmpty(paymentPlanItemList)) {
                // 校验付款计划状态
                for (BizReceiptPaymentPlanItem paymentPlanItem : paymentPlanItemList) {
                    BizReceiptPaymentPlanHead paymentPlanHead = bizReceiptPaymentPlanHeadDataWrap.getById(paymentPlanItem.getHeadId());
                    if (UtilObject.isNotNull(paymentPlanHead) &&!EnumReceiptStatus.RECEIPT_STATUS_UN_COMPILE.getValue().equals(paymentPlanHead.getReceiptStatus())) {
                        throw new WmsException(EnumReturnMsg.RETURN_ERROR_PAYMENT_PLAN_STATUS_CANNOT_REVOKE, paymentPlanHead.getReceiptCode());
                    }
                }

                // 删除付款计划
                List<Long> paymentPlanHeadIdList = paymentPlanItemList.stream().map(BizReceiptPaymentPlanItem::getHeadId).collect(Collectors.toList());
                bizReceiptPaymentPlanItemDataWrap.remove(new LambdaQueryWrapper<BizReceiptPaymentPlanItem>().in(BizReceiptPaymentPlanItem::getHeadId, paymentPlanHeadIdList));
                bizReceiptPaymentPlanHeadDataWrap.remove(new LambdaQueryWrapper<BizReceiptPaymentPlanHead>().in(BizReceiptPaymentPlanHead::getId, paymentPlanHeadIdList));
                // 删除单据流
                receiptRelationService.deleteReceiptTree(paymentPlanHeadIdList, EnumReceiptType.PAYMENT_PLAN.getValue());
            }

            // 7. 查询并校验合同是否关联
            List<BizReceiptContractHead> contractHeadList = bizReceiptContractHeadDataWrap.list(new LambdaQueryWrapper<BizReceiptContractHead>().eq(BizReceiptContractHead::getDeliveryCode, headDTO.getReceiptCode()));
            if (UtilCollection.isNotEmpty(contractHeadList)) {
                throw new WmsException(EnumReturnMsg.RETURN_ERROR_PAYMENT_PLAN_STATUS_CANNOT_REVOKE, contractHeadList.get(0).getReceiptCode());
            }
        }

        // 更新送货通知状态为草稿
        this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
    }


    public void  getSupplierList(BizContext ctx){
        // 从上下文获取查询条件对象
        DicSupplierPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 查询供应商
        LambdaQueryWrapper<DicSupplier> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UtilNumber.isNotEmpty(po.getIsInlandAgent()), DicSupplier::getIsInlandAgent, po.getIsInlandAgent());
        queryWrapper.like(UtilString.isNotNullOrEmpty(po.getSupplierCode()), DicSupplier::getSupplierCode, po.getSupplierCode());
        queryWrapper.like(UtilString.isNotNullOrEmpty(po.getSupplierName()), DicSupplier::getSupplierName, po.getSupplierName());
        List<DicSupplier> supplierList = dicSupplierDataWrap.list(queryWrapper);
        // 属性填充
        List<DicSupplierDTO> supplierDTOList = UtilCollection.toList(supplierList, DicSupplierDTO.class);
        dataFillService.fillAttr(supplierDTOList);
        // 设置上下文VO
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, supplierDTOList);
    }


    /**
     * 离岸送货通知单-审批通过后 未生成物流清关补偿接口
     */
    public void doCompensateLogistics(BizContext ctx) {
        BizReceiptDeliveryNoticeHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        BizReceiptDeliveryNoticeHead head = deliveryNoticeHeadDataWrap.getById(po.getId());
        BizReceiptDeliveryNoticeHeadDTO headDTO = UtilBean.newInstance(head, BizReceiptDeliveryNoticeHeadDTO.class);
        dataFillService.fillAttr(headDTO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
        // 更新状态已完成
        this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
        // 生成物流清关费用
        this.genLogistics(ctx);
    }


    private String getMonth() {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 获取下个月
        YearMonth nextMonth = YearMonth.from(currentDate).plusMonths(1);
        // 格式化为 "yyyy-MM" 的字符串
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        return nextMonth.format(formatter);

    }

    /**
     * 生成付款计划
     *
     * @param ctx
     */
    public void genPaymentPlan(BizContext ctx) {
        BizReceiptDeliveryNoticeHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 排除油品送货
        if (!headDTO.getSendType().equals(EnumSendType.OIL_PROCUREMENT.getValue())) {
            // 按照行项目的合同号分组（注意这里的合同号和抬头的不一致）
            Map<String, List<BizReceiptDeliveryNoticeItemDTO>> contractMap = headDTO.getItemList().stream()
                    .collect(Collectors.groupingBy(BizReceiptDeliveryNoticeItemDTO::getContractCode));
            contractMap.forEach((k, v) -> {
                BizReceiptContractHead contractHead = bizReceiptContractHeadDataWrap.getOne(
                        new QueryWrapper<BizReceiptContractHead>().lambda()
                                .eq(BizReceiptContractHead::getReceiptCode, k));
                if (Objects.nonNull(contractHead)) {
                    BizReceiptContractHeadDTO contractHeadDTO = UtilBean.newInstance(contractHead, BizReceiptContractHeadDTO.class);
                    dataFillService.fillAttr(contractHeadDTO);
                    if (UtilCollection.isNotEmpty(contractHeadDTO.getNodeList())) {
                        // 处理发货款 NODE_3
                        Optional<BizReceiptContractPaymentNodeDTO> node3Opt = contractHeadDTO.getNodeList().stream()
                                .filter(e -> e.getPaymentNode().equals(EnumContractPaymentNode.NODE_3.getCode()))
                                .findFirst();
                        node3Opt.ifPresent(nodeDTO -> generatePaymentPlanForNode(ctx, contractHead, contractHeadDTO, v, nodeDTO, EnumContractPaymentNode.NODE_3));

                        // 处理到货款 NODE_4
                        // Optional<BizReceiptContractPaymentNodeDTO> node4Opt = contractHeadDTO.getNodeList().stream()
                        //         .filter(e -> e.getPaymentNode().equals(EnumContractPaymentNode.NODE_4.getCode()))
                        //         .findFirst();
                        // node4Opt.ifPresent(nodeDTO -> generatePaymentPlanForNode(ctx, contractHead, contractHeadDTO, v, nodeDTO, EnumContractPaymentNode.NODE_4));
                    }

                }
            });
        }
    }

    public Integer getPlanTypeByPurchaseType(Integer purchaseType) {

        List<Integer> list = new ArrayList<>();
        list.add(EnumPurchaseType.NON_PRODUCTION_MATERIAL.getCode());
        list.add(EnumPurchaseType.SERVICE.getCode());
        list.add(EnumPurchaseType.CONSTRUCTION.getCode());
        if (list.contains(purchaseType)) {
            return 2;
        }
        if (EnumPurchaseType.ASSET.getCode().equals(purchaseType)) {
            return 4;
        }
        return 1;
    }

    private void generatePaymentPlanForNode(BizContext ctx,
                                            BizReceiptContractHead contractHead,
                                            BizReceiptContractHeadDTO contractHeadDTO,
                                            List<BizReceiptDeliveryNoticeItemDTO> items,
                                            BizReceiptContractPaymentNodeDTO nodeDTO,
                                            EnumContractPaymentNode nodeType) {
        BizReceiptPaymentPlanHeadDTO paymentPlanHeadDTO = new BizReceiptPaymentPlanHeadDTO();
        paymentPlanHeadDTO.setId(null);
        paymentPlanHeadDTO.setReceiptType(EnumReceiptType.PAYMENT_PLAN.getValue());

        paymentPlanHeadDTO.setPlanType(this.getPlanTypeByPurchaseType(contractHead.getPurchaseType()));

        paymentPlanHeadDTO.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_UN_COMPILE.getValue());
        paymentPlanHeadDTO.setReceiptCode(bizCommonService.getNextSequenceValueDaily(EnumSequenceCode.PAYMENT_PLAN.getValue()));
        paymentPlanHeadDTO.setCreateUserId(ctx.getCurrentUserId());
        paymentPlanHeadDTO.setContractId(contractHead.getId());
        paymentPlanHeadDTO.setPaymentMonth(this.getMonth());
        paymentPlanHeadDTO.setPaymentNode(nodeType.getCode());
        paymentPlanHeadDTO.setRate(nodeDTO.getRate());

        if (UtilCollection.isNotEmpty(contractHeadDTO.getItemList())) {
            paymentPlanHeadDTO.setTaxCodeRate(contractHeadDTO.getItemList().get(0).getTaxCodeRate());
            paymentPlanHeadDTO.setContractAmount(contractHeadDTO.getItemList().stream()
                    .map(BizReceiptContractItemDTO::getTaxAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
        }

        bizReceiptPaymentPlanHeadDataWrap.saveDto(paymentPlanHeadDTO);

        BigDecimal totalAmount = BigDecimal.ZERO;
        List<BizReceiptPaymentPlanItemDTO> paymentPlanItemDTOS = new ArrayList<>();

        for (BizReceiptDeliveryNoticeItemDTO itemDTO : items) {
            BizReceiptPaymentPlanItemDTO paymentPlanItemDTO = new BizReceiptPaymentPlanItemDTO();
            UtilBean.copy(itemDTO, paymentPlanItemDTO);
            paymentPlanItemDTO.setId(null);
            paymentPlanItemDTO.setHeadId(paymentPlanHeadDTO.getId());
            paymentPlanItemDTO.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_UN_COMPILE.getValue());
            paymentPlanItemDTO.setQty(itemDTO.getQty());
            paymentPlanItemDTO.setCreateUserId(ctx.getCurrentUserId());
            // 已经生成采购订单了
            paymentPlanItemDTO.setPurchaseReceiptCode(itemDTO.getPurchaseCode());
            paymentPlanItemDTO.setPurchaseReceiptRid(itemDTO.getPurchaseRid());
            paymentPlanItemDTO.setPreReceiptType(itemDTO.getReceiptType());
            paymentPlanItemDTO.setPreReceiptItemId(itemDTO.getId());
            paymentPlanItemDTO.setPreReceiptHeadId(itemDTO.getHeadId());
            paymentPlanItemDTOS.add(paymentPlanItemDTO);

            if (itemDTO.getQty() != null && itemDTO.getTaxPrice() != null) {
                totalAmount = totalAmount.add(itemDTO.getQty().multiply(itemDTO.getTaxPrice()));
            }
        }

        paymentPlanHeadDTO.setQty(totalAmount.multiply(nodeDTO.getRate()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
        bizReceiptPaymentPlanHeadDataWrap.updateDtoById(paymentPlanHeadDTO);
        bizReceiptPaymentPlanItemDataWrap.saveBatchDto(paymentPlanItemDTOS);
        // 保存单据流
        List<BizCommonReceiptRelation> dtoList = new ArrayList<>();
        for (BizReceiptPaymentPlanItemDTO item : paymentPlanItemDTOS) {
            BizCommonReceiptRelation dto = new BizCommonReceiptRelation().setReceiptType(paymentPlanHeadDTO.getReceiptType())
                    .setReceiptHeadId(item.getHeadId()).setReceiptItemId(item.getId())
                    .setPreReceiptType(item.getPreReceiptType()).setPreReceiptHeadId(item.getPreReceiptHeadId())
                    .setPreReceiptItemId(item.getPreReceiptItemId());
            dtoList.add(dto);
        }
        if (UtilCollection.isNotEmpty(dtoList)) {
            receiptRelationService.multiSaveReceiptTree(dtoList);
        }
    }

}
