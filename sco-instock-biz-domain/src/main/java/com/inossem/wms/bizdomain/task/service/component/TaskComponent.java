package com.inossem.wms.bizdomain.task.service.component;

//import com.alibaba.excel.util.CollectionUtils;

import com.alibaba.excel.exception.ExcelAnalysisException;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.inossem.wms.bizbasis.batch.service.biz.BatchImgService;
import com.inossem.wms.bizbasis.batch.service.biz.BatchInfoService;
import com.inossem.wms.bizbasis.batch.service.datawrap.BizBatchInfoDataWrap;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.common.service.biz.I18nTextCommonService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptOperationLogService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptRelationService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptTypeService;
import com.inossem.wms.bizbasis.rfid.service.biz.LabelReceiptRelService;
import com.inossem.wms.bizbasis.stock.service.biz.StockCommonService;
import com.inossem.wms.bizbasis.stock.service.datawrap.StockBatchDataWrap;
import com.inossem.wms.bizbasis.stock.service.datawrap.StockOccupyDataWrap;
import com.inossem.wms.bizdomain.input.service.datawrap.BizReceiptInputItemDataWrap;
import com.inossem.wms.bizdomain.output.service.datawrap.BizReceiptOutputHeadDataWrap;
import com.inossem.wms.bizdomain.task.service.datawrap.BizReceiptTaskHeadDataWrap;
import com.inossem.wms.bizdomain.task.service.datawrap.BizReceiptTaskItemDataWrap;
import com.inossem.wms.bizdomain.task.service.datawrap.BizReceiptTaskReqHeadDataWrap;
import com.inossem.wms.bizdomain.task.service.datawrap.BizReceiptTaskReqItemDataWrap;
import com.inossem.wms.bizdomain.task.service.strategy.unload.UnloadRecommendService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.constant.task.TaskConst;
import com.inossem.wms.common.enums.EnumDefaultStorageType;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.enums.EnumReceiptOperationType;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.EnumStockStatus;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.batch.dto.BizBatchImgDTO;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.batch.entity.BizBatchInfo;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptRelation;
import com.inossem.wms.common.model.bizdomain.input.entity.BizReceiptInputItem;
import com.inossem.wms.common.model.bizdomain.output.entity.BizReceiptOutputHead;
import com.inossem.wms.common.model.bizdomain.task.dto.BizReceiptTaskHeadDTO;
import com.inossem.wms.common.model.bizdomain.task.dto.BizReceiptTaskItemDTO;
import com.inossem.wms.common.model.bizdomain.task.dto.BizReceiptTaskReqHeadDTO;
import com.inossem.wms.common.model.bizdomain.task.dto.BizReceiptTaskReqItemDTO;
import com.inossem.wms.common.model.bizdomain.task.entity.BizReceiptTaskHead;
import com.inossem.wms.common.model.bizdomain.task.entity.BizReceiptTaskItem;
import com.inossem.wms.common.model.bizdomain.task.entity.BizReceiptTaskReqHead;
import com.inossem.wms.common.model.bizdomain.task.entity.BizReceiptTaskReqItem;
import com.inossem.wms.common.model.bizdomain.task.po.BizReceiptTaskReqSavePo;
import com.inossem.wms.common.model.bizdomain.task.po.BizReceiptTaskRollbackPO;
import com.inossem.wms.common.model.bizdomain.task.po.BizReceiptTaskSearchPo;
import com.inossem.wms.common.model.bizdomain.task.vo.*;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.ButtonVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.file.file.entity.BizCommonFile;
import com.inossem.wms.common.model.label.dto.BizLabelDataDTO;
import com.inossem.wms.common.model.label.dto.BizLabelReceiptRelDTO;
import com.inossem.wms.common.model.label.entity.BizLabelReceiptRel;
import com.inossem.wms.common.model.masterdata.base.entity.DicUnit;
import com.inossem.wms.common.model.masterdata.mat.fty.dto.DicMaterialFactoryDTO;
import com.inossem.wms.common.model.masterdata.mat.info.dto.DicMaterialDTO;
import com.inossem.wms.common.model.masterdata.storagebin.dto.DicWhStorageBinDTO;
import com.inossem.wms.common.model.org.location.dto.DicStockLocationDTO;
import com.inossem.wms.common.model.org.wh.entity.DicWh;
import com.inossem.wms.common.model.stock.dto.StockBinDTO;
import com.inossem.wms.common.model.stock.dto.StockInsMoveTypeDTO;
import com.inossem.wms.common.model.stock.entity.StockBatch;
import com.inossem.wms.common.model.stock.entity.StockOccupy;
import com.inossem.wms.common.model.stock.po.StockBinPO;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import com.inossem.wms.common.util.*;
import com.inossem.wms.common.util.excel.UtilExcel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TaskComponent {

    @Autowired
    protected DataFillService dataFillService;

    @Autowired
    protected BatchImgService bizBatchImgService;

    @Autowired
    protected DictionaryService dictionaryService;

    @Autowired
    protected ReceiptOperationLogService receiptOperationLogService;

    @Autowired
    private BizReceiptTaskReqHeadDataWrap bizReceiptTaskReqHeadDataWrap;

    @Autowired
    private BizReceiptTaskReqItemDataWrap bizReceiptTaskReqItemDataWrap;

    @Autowired
    private BizReceiptTaskHeadDataWrap bizReceiptTaskHeadDataWrap;

    @Autowired
    private BizReceiptTaskItemDataWrap bizReceiptTaskItemDataWrap;

    @Autowired
    protected I18nTextCommonService i18nTextCommonService;
    @Autowired
    private ReceiptTypeService receiptTypeService;

    @Autowired
    private TaskMoveTypeComponent taskMoveTypeComponent;

    @Autowired
    private StockCommonService stockCommonService;

    @Autowired
    private ReceiptRelationService receiptRelationService;

    @Autowired
    private LabelReceiptRelService labelReceiptRelService;
    @Autowired
    private BizReceiptOutputHeadDataWrap bizReceiptOutputHeadDataWrap;
    @Autowired
    private BizReceiptInputItemDataWrap bizReceiptInputItemDataWrap;
    @Autowired
    private StockOccupyDataWrap stockOccupyDataWrap;
    @Autowired
    protected StockBatchDataWrap stockBatchDataWrap;
    @Autowired
    private BizBatchInfoDataWrap batchInfoDataWrap;

    @Autowired
    private BatchInfoService batchInfoService;

    /**
     * 获取作业请求列表页-分页
     *
     * @param ctx 系统上下文
     * @return 分页类型的集合
     */
    public void getTaskReqPageList(BizContext ctx) {
        log.info("作业请求列表查询 ctx：{}", JSONObject.toJSONString(ctx, SerializerFeature.IgnoreNonFieldGetter));
        /* ************************ 获取上下文参数 **********************************/
        BizReceiptTaskSearchPo po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser user = ctx.getCurrentUser();
        List<Long> locationIdList =null;
        if(user!=null &&user.getLocationList()!=null){
            List<DicStockLocationDTO> locationList =user.getLocationList();
            locationIdList =locationList.stream().map(DicStockLocationDTO::getId).collect(Collectors.toList());
        }
        po.setLocationIdList(locationIdList);
        // 分页处理
        IPage<BizReceiptTaskVO> page = new Page<>(po.getPageIndex(), po.getPageSize());
        List<BizReceiptTaskVO> taskVoList = bizReceiptTaskReqHeadDataWrap.selectStockTaskReqList(po, page);
        log.info("获取作业请求列表 taskVoList：{}", JSONObject.toJSONString(taskVoList));
        // 数据填充
        dataFillService.fillAttr(taskVoList);
        for (BizReceiptTaskVO taskVO : taskVoList) {
            taskVO.setPreApplyReceiptCode(taskVO.getReqItemList().get(0).getPreApplyReceiptCode());
        }
        log.info("获取作业请求列表-填充 taskVoList：{}", JSONObject.toJSONString(taskVoList));
        // 回填数据到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(taskVoList, page.getTotal()));
    }

    /**
     * 根据请求id获取作业请求详情
     *
     * @param ctx 系统上下文
     */
    public void getTaskReq(BizContext ctx) {
        log.info("作业请求查询 ctx：{}", JSONObject.toJSONString(ctx));
        /* ************************ 获取上下文参数 **********************************/
        Long id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(id)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        /* ***************************请求抬头信息 **********************************/
        // 请求头信息
        BizReceiptTaskReqHead bizReceiptTaskReqHead = bizReceiptTaskReqHeadDataWrap.getById(id);
        // 转DTO
        BizReceiptTaskReqHeadDTO bizReceiptTaskReqHeadDTO =
            UtilBean.newInstance(bizReceiptTaskReqHead, BizReceiptTaskReqHeadDTO.class);
        // 数据填充
        dataFillService.fillAttr(bizReceiptTaskReqHeadDTO);
        if (!bizReceiptTaskReqHeadDTO.getItemList().get(0).getPreReceiptType().equals(EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ.getValue())) {
            // 不是领用出库, 没有是否准将预留
            bizReceiptTaskReqHeadDTO.setIsExact(-1);
        } else {
            bizReceiptTaskReqHeadDTO.setIsExact(bizReceiptTaskReqHeadDTO.getItemList().get(0).getIsExact());
        }
        /* ***************************作业单信息 **********************************/
        // 查询条件设置
        QueryWrapper<BizReceiptTaskItem> wrapper = new QueryWrapper<>();
        // 拼装参数
        wrapper.lambda().eq(BizReceiptTaskItem::getTaskReqHeadId, id);
        // 回填taskItemList
        List<BizReceiptTaskItem> taskItemList = bizReceiptTaskItemDataWrap.list(wrapper);
        List<BizReceiptTaskItemDTO> taskItemDTOList = UtilCollection.toList(taskItemList, BizReceiptTaskItemDTO.class);
        // 数据填充
        dataFillService.fillRlatAttrDataList(taskItemDTOList);
        /* ***************************操作权限信息 **********************************/
        // 按钮组权限
        ButtonVO buttonVO = this.setButton(bizReceiptTaskReqHeadDTO.getReceiptStatus());
        // 扩展功能权限
        ExtendVO extendVO = this.setExtend(bizReceiptTaskReqHeadDTO.getReceiptStatus());
        // 上下文返回参数
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(bizReceiptTaskReqHeadDTO, extendVO, buttonVO));
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_REF_PO, taskItemDTOList);
    }

    /**
     * 生成ins凭证
     *
     * @param ctx 系统上下文
     */
    public void generateTaskInsDoc(BizContext ctx) {
        log.info("生成ins凭证 ctx：{}", JSONObject.toJSONString(ctx));
        /* ************************ 获取上下文参数 **********************************/
        BizReceiptTaskHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_REF_PO);
        if (null == headDTO) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 凭证
        StockInsMoveTypeDTO insMoveTypeDTO = null;
        // 上架
        if (EnumReceiptType.STOCK_TASK_SHELF_LOAD.getValue().equals(headDTO.getReceiptType())) {
            // 生成凭证
            insMoveTypeDTO = taskMoveTypeComponent.generateTaskInsDoc(headDTO);

        } else // 下架
        if (EnumReceiptType.STOCK_TASK_SHELF_UNLOAD.getValue().equals(headDTO.getReceiptType())) {
            insMoveTypeDTO = taskMoveTypeComponent.generateTaskInsDoc(headDTO);
        }
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_INS, insMoveTypeDTO);
    }

    /**
     * 库存校验和数量计算
     *
     * @param ctx 系统上下文
     */
    public void checkAndComputeForModifyStock(BizContext ctx) {
        log.info("库存校验和数量计算 ctx：{}", JSONObject.toJSONString(ctx));
        /* ************************ 获取上下文参数 **********************************/
        StockInsMoveTypeDTO insMoveTypeDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_INS);
        stockCommonService.checkAndComputeForModifyStock(insMoveTypeDTO);
    }

    /**
     * 修改库存
     *
     * @param ctx 系统上下文
     */
    public void modifyStock(BizContext ctx) {
        log.info("修改库存 ctx：{}", JSONObject.toJSONString(ctx));
        /* ************************ 获取上下文参数 **********************************/
        StockInsMoveTypeDTO insMoveTypeDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_INS);
        stockCommonService.modifyStock(insMoveTypeDTO);
    }

    /**
     * 修改单据状态 请求：item：请求数量是否已全部作业（qty=task_qty）-已完成，否则未完成 head：全部的item是否已完成 作业：item：是否已全部已完成-已完成，否则未完成 head：全部的item是否已完成
     *
     * @param ctx 系统上下文
     */
    public void updateReqStatus(BizContext ctx) {
        log.info("修改请求状态 ctx：{}", JSONObject.toJSONString(ctx));
        /* ************************ 获取上下文参数 **********************************/
        // 请求参数
        BizReceiptTaskReqSavePo po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        BizReceiptTaskReqHeadDTO reqHead = po.getStockTaskReqHeadInfo();
        // 作业单参数
        BizReceiptTaskHeadDTO taskHead = ctx.getContextData(Const.BIZ_CONTEXT_KEY_REF_PO);
        List<BizReceiptTaskItemDTO> taskItemDTOList = taskHead.getBizReceiptTaskItemDTOList();
        /* ************************ 修改请求 **********************************/
        List<BizReceiptTaskReqItemDTO> reqItemList = new ArrayList<>();
        // 单据行项目完成状态==》当所有行项目的状态为已完成则请求状态为已完成
        reqHead.getItemList().forEach(reqItem -> {
            // 作业数量
            BigDecimal taskQty = null == reqItem.getTaskQty() ? BigDecimal.ZERO : reqItem.getTaskQty();
            // 提交数量
            BigDecimal submitQty = null == reqItem.getSubmitQty() ? BigDecimal.ZERO : reqItem.getSubmitQty();
            // 作业数量=请求数量 ==》单据状态为已完成 (超发时,提交数量可以大于单据数量)
            if (taskQty.add(submitQty).compareTo(reqItem.getQty()) >= 0) {
                reqItem.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
            }
            reqItem.setTaskQty(taskQty.add(submitQty));
            reqItemList.add(reqItem);
        });
        // 更新item状态
        bizReceiptTaskReqItemDataWrap.updateBatchDtoById(reqItemList);
        QueryWrapper<BizReceiptTaskReqItem> reqItemWrapper = new QueryWrapper<>();
        reqItemWrapper.lambda().eq(BizReceiptTaskReqItem::getHeadId, reqHead.getId());
        // 校验全部行项目状态
        List<BizReceiptTaskReqItem> taskReqItemList = bizReceiptTaskReqItemDataWrap.list(reqItemWrapper);
        Map<Integer, List<BizReceiptTaskReqItem>> taskReqItemMap =
            taskReqItemList.stream().collect(Collectors.groupingBy(BizReceiptTaskReqItem::getItemStatus));
        // 更新head状态
        if (taskReqItemMap.size() == 1
            && taskReqItemMap.keySet().toArray()[0].equals(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue())) {
            reqHead.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
            bizReceiptTaskReqHeadDataWrap.updateDtoById(reqHead);
        }
        /* ************************ 修改作业单 **********************************/
        // 修改行项目状态-已完成
        taskItemDTOList
            .forEach(taskItem -> taskItem.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue()));
        bizReceiptTaskItemDataWrap.updateBatchDtoById(taskItemDTOList);
        QueryWrapper<BizReceiptTaskItem> taskItemWrapper = new QueryWrapper<>();
        taskItemWrapper.lambda().eq(BizReceiptTaskItem::getHeadId, taskHead.getId());
        // 校验全部行项目状态
        List<BizReceiptTaskItem> taskItemList = bizReceiptTaskItemDataWrap.list(taskItemWrapper);
        Map<Integer, List<BizReceiptTaskItem>> taskItemMap =
            taskItemList.stream().collect(Collectors.groupingBy(BizReceiptTaskItem::getItemStatus));
        // 更新head状态
        if (taskItemMap.size() == 1
            && taskItemMap.keySet().toArray()[0].equals(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue())) {
            taskHead.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
            bizReceiptTaskHeadDataWrap.updateDtoById(taskHead);
        }
    }

    /* *************************************************校验*************************************************************/
    /**
     * 作业请求数据校验
     *
     * @param ctx 系统上下文
     */
    public void CheckSaveData(BizContext ctx) {
        log.info("作业请求数据校验 ctx：{}", JSONObject.toJSONString(ctx));
        /* ************************ 获取上下文参数 **********************************/
        BizReceiptTaskReqSavePo po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        /* ************************ 空行项目校验 **********************************/
        if (null == po) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        if (UtilCollection.isEmpty(po.getStockTaskReqHeadInfo().getItemList())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
        /* ************************ 请求行项目操作数量为0校验，去除操作数量为0的行项目 **********************************/
        // 行项目
        List<BizReceiptTaskReqItemDTO> stockTaskReqItemDTOList = po.getStockTaskReqHeadInfo().getItemList();
        stockTaskReqItemDTOList.removeIf(taskReqItem -> taskReqItem.getSubmitQty() == null
            || taskReqItem.getSubmitQty().compareTo(BigDecimal.ZERO) <= 0);
        if (UtilCollection.isEmpty(stockTaskReqItemDTOList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
        /* ************************ 作业请求合法性校验 **********************************/
        BizReceiptTaskReqHeadDTO reqHeadDTO = po.getStockTaskReqHeadInfo();
        Long headId = reqHeadDTO.getId();
        BizReceiptTaskReqHead bizReceiptTaskReqHead = bizReceiptTaskReqHeadDataWrap.getById(headId);
        if (null == bizReceiptTaskReqHead) {
            throw new WmsException(EnumReturnMsg.RETURN_REQ_IS_DELETE_NOT_SUBMIT);
        }
        if (EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().intValue() == bizReceiptTaskReqHead.getReceiptStatus()
            .intValue()) {
            throw new WmsException(EnumReturnMsg.RETURN_REQ_IS_COMPLETE_NOT_SUBMIT);
        }
        if (EnumRealYn.TRUE.getIntValue().equals(bizReceiptTaskReqHead.getIsDelete())) {
            throw new WmsException(EnumReturnMsg.RETURN_REQ_IS_DELETE_NOT_SUBMIT);
        }
        /* ************************ 行项目与仓位仓库是否一致 **********************************/
        Set<String> errorWhBinSet = new HashSet<>();
        Set<Long> errorBinSet = new HashSet<>();
        List<RecommendStockBinVO> errorDefaultBinList = new ArrayList<>();
        po.getStockTaskReqHeadInfo().getItemList().forEach(item -> {
            if (UtilCollection.isNotEmpty(item.getStockBinList())) {
                List<StockBinDTO> stockBinList = item.getStockBinList();
                UtilCollection.toList(stockBinList, RecommendStockBinVO.class).forEach(bin -> {
                    // 获取bin信息
                    DicWhStorageBinDTO binDTO = dictionaryService.getBinCacheById(bin.getBinId());
                    // 空仓位
                    if (null == binDTO) {
                        errorBinSet.add(bin.getBinId());
                    } else {
                        // 行项目的仓库和提交仓位的仓库是否一致
                        if (!item.getWhId().equals(binDTO.getWhId())) {
                            errorWhBinSet.add(item.getRid());
                        }
                        // 提交仓位是否为默认仓位-根据存储类型是否为临时存储类型
                        if (UtilConst.getInstance().getDefaultStorageTypeCodeSet().contains(bin.getTypeCode())) {
                            errorDefaultBinList.add(bin);
                        }
                    }
                });
            }
        });
        if (UtilCollection.isNotEmpty(errorBinSet)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_BIN_ERROR, errorBinSet.toString());
        }
        if (UtilCollection.isNotEmpty(errorWhBinSet)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_ITEM_WH_NOT_EQUALS_BIN_WH, errorWhBinSet.toString());
        }
        if (UtilCollection.isNotEmpty(errorDefaultBinList)) {
            Set<String> whCodeSet = new HashSet<>(), typeCodeSet = new HashSet<>(), binCodeSet = new HashSet<>();
            errorDefaultBinList.forEach(bin -> {
                whCodeSet.add(bin.getWhCode());
                typeCodeSet.add(bin.getTypeCode());
                binCodeSet.add(bin.getBinCode());
            });
            throw new WmsException(EnumReturnMsg.RETURN_CODE_NOT_CONFIGURATION_BIN_CODE, whCodeSet.toString(),
                typeCodeSet.toString(), binCodeSet.toString());
        }
    }

    /**
     * 刷新请求数量
     *
     * @param ctx 系统上下文
     */
    public void refreshReqInfo(BizContext ctx) {
        log.info("刷新请求数量 ctx：{}", JSONObject.toJSONString(ctx));
        BizReceiptTaskReqSavePo po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (null == po) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 行项目空校验
        if (UtilCollection.isEmpty(po.getStockTaskReqHeadInfo().getItemList())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
        Map<Long,
            BizReceiptTaskReqItemDTO> itemIdMap = po.getStockTaskReqHeadInfo().getItemList().stream()
                .collect(Collectors.groupingBy(BizReceiptTaskReqItemDTO::getId,
                    Collectors.collectingAndThen(Collectors.toList(), items -> items.get(0))));
        List<BizReceiptTaskReqItem> bizReceiptTaskReqItems =
            bizReceiptTaskReqItemDataWrap.listByIds(itemIdMap.keySet());
        // 组装itemList
        List<BizReceiptTaskReqItemDTO> itemDTOList = new ArrayList<>();
        bizReceiptTaskReqItems.forEach(item -> {
            BizReceiptTaskReqItemDTO oldItem = itemIdMap.get(item.getId());
            oldItem.setQty(item.getQty());
            oldItem.setTaskQty(item.getTaskQty());
            oldItem.setItemStatus(item.getItemStatus());
            itemDTOList.add(oldItem);
        });
        po.getStockTaskReqHeadInfo().setItemList(itemDTOList);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, po);
    }

    /**
     * 数量校验 本次操作数量 小于等于未操作架数量(上下架通用)
     *
     * @param ctx 系统上下文
     */
    public void checkRemainderQty(BizContext ctx) {
        log.info("数量校验 ctx：{}", JSONObject.toJSONString(ctx));
        BizReceiptTaskReqSavePo po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (null == po) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 行项目空校验
        if (UtilCollection.isEmpty(po.getStockTaskReqHeadInfo().getItemList())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
        Set<String> errorSet = new HashSet<>();
        po.getStockTaskReqHeadInfo().getItemList().forEach(item -> {
            if (UtilNumber.isEmpty(item.getIsOver())) {
                BigDecimal remainderQty = item.getQty().subtract(item.getTaskQty());
                if (remainderQty.compareTo(item.getSubmitQty()) < 0) {
                    errorSet.add(item.getRid());
                }
            }
        });
        if (!errorSet.isEmpty()) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_ITEM_QTY_NOT_ENOUGH, errorSet.toString());
        }
        // 超发则下架可选择超过数量的库存, 最大值 : 库存-已占用
        Integer referReceiptType = po.getStockTaskReqHeadInfo().getItemList().get(0).getReferReceiptType();
        if (UtilNumber.isNotEmpty(referReceiptType) && referReceiptType.equals(EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ_APPLY_BY_REQUIRE.getValue())) {

            List<Long> stockBatchIdList = new ArrayList<>();
            Map<Long, BigDecimal> qtyMap = new HashMap<>();
            for (BizReceiptTaskReqItemDTO itemDTO : po.getStockTaskReqHeadInfo().getItemList()) {
                for (StockBinDTO binDTO : itemDTO.getStockBinList()) {
                    Long key = binDTO.getStockBatchId();
                    stockBatchIdList.add(key);
                    if (qtyMap.isEmpty() || !qtyMap.containsKey(key)) {
                        qtyMap.put(key, binDTO.getOperationQty());
                    } else {
                        qtyMap.put(key, qtyMap.get(key).add(binDTO.getOperationQty()));
                    }
                }
            }
            Long applyReceiptHeadId = po.getStockTaskReqHeadInfo().getItemList().get(0).getReferReceiptHeadId();
            List<StockOccupy> stockOccupyList = stockOccupyDataWrap.list(new QueryWrapper<StockOccupy>().lambda().in(StockOccupy::getStockBatchId, stockBatchIdList).ne(StockOccupy::getReceiptHeadId, applyReceiptHeadId));
            Map<Long, BigDecimal> occupyMap = new HashMap<>();
            for (StockOccupy occupy : stockOccupyList) {
                Long key = occupy.getStockBatchId();
                if (qtyMap.isEmpty() || !qtyMap.containsKey(key)) {
                    occupyMap.put(key, occupy.getQty());
                } else {
                    occupyMap.put(key, qtyMap.get(key).add(occupy.getQty()));
                }
            }
            List<StockBatch> stockBatchList = stockBatchDataWrap.listByIds(stockBatchIdList);
            for (StockBatch stockBatch : stockBatchList) {
                Long key = stockBatch.getId();
                BigDecimal operationQty = qtyMap.get(key);
                BigDecimal stockQty = stockBatch.getQty();
                BigDecimal occupyQty = occupyMap.get(key);
                if(UtilNumber.isEmpty(occupyQty)){
                    occupyQty = BigDecimal.ZERO;
                }
                if (operationQty.compareTo(stockQty.subtract(occupyQty)) > 0) {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_IMP_QTR, "操作数量" + operationQty + ",大于库存数量" + stockQty + "-占用数量" + occupyQty);
                }
            }
        }
    }

    /* *************************************************作业通用*************************************************************/
    /**
     * 默认存储类型id 仓位id
     *
     * @param receptType 单据类型
     * @param preRecepitType 前续单据类型
     * @param whId 仓库id
     * @return Map<String, Object>
     */
    Map<String, Object> getDefaultBin(Integer receptType, Integer preRecepitType, Long whId) {
        Map<String, Object> resultMap = new HashMap<>();
        // 上架临时存储区
        String typeCode = Const.STRING_EMPTY;
        String binCode = Const.STRING_EMPTY;
        // 上架
        if (EnumReceiptType.STOCK_TASK_SHELF_LOAD.getValue().equals(receptType)) {
            // 上架存储类型、仓位默认值
            typeCode = EnumDefaultStorageType.INPUT.getTypeCode();
            binCode = EnumDefaultStorageType.INPUT.getBinCode();
            if (receiptTypeService.isHasteInput(preRecepitType)) {
                typeCode = EnumDefaultStorageType.INPUT.getTypeCode();
                binCode = EnumDefaultStorageType.INPUT.getBinCode();
            } else if (receiptTypeService.isTempInput(preRecepitType)) {
                typeCode = EnumDefaultStorageType.INPUT.getTypeCode();
                binCode = EnumDefaultStorageType.INPUT.getBinCode();
            } else if (receiptTypeService.getInputReceiptType().contains(preRecepitType)) {
                // 入库临时存储区
                typeCode = EnumDefaultStorageType.INPUT.getTypeCode();
                binCode = EnumDefaultStorageType.INPUT.getBinCode();
            } else if (receiptTypeService.getReturnReceiptType().contains(preRecepitType)) {
                // 出库退库临时存储区
                typeCode = EnumDefaultStorageType.OUTPUT_RETURN.getTypeCode();
                binCode = EnumDefaultStorageType.OUTPUT_RETURN.getBinCode();
            } else if (receiptTypeService.getTransportReceiptType().contains(preRecepitType)
                || EnumReceiptType.HASTE_STOCK_TRANSPORT.getValue().equals(preRecepitType)) {
                // 转储临时存储区(不区分正向与冲销)
                typeCode = EnumDefaultStorageType.TRANSPORT.getTypeCode();
                binCode = EnumDefaultStorageType.TRANSPORT.getBinCode();
            } else if (receiptTypeService.getOutputReceiptType().contains(preRecepitType)) {
                // 冲销临时存储区
                typeCode = EnumDefaultStorageType.WRITE_OFF.getTypeCode();
                binCode = EnumDefaultStorageType.WRITE_OFF.getBinCode();
            } else if (receiptTypeService.isHasteOutput(preRecepitType)) {
                // 冲销临时区
                typeCode = EnumDefaultStorageType.WRITE_OFF.getTypeCode();
                binCode = EnumDefaultStorageType.WRITE_OFF.getBinCode();
            } else if (receiptTypeService.isTempOutput(preRecepitType)) {
                // 冲销临时区
                typeCode = EnumDefaultStorageType.WRITE_OFF.getTypeCode();
                binCode = EnumDefaultStorageType.WRITE_OFF.getBinCode();
            }
        } else // 下架
        if (EnumReceiptType.STOCK_TASK_SHELF_UNLOAD.getValue().equals(receptType)) {
            // 下架存储类型、仓位默认值
            typeCode = EnumDefaultStorageType.OUTPUT.getTypeCode();
            binCode = EnumDefaultStorageType.OUTPUT.getBinCode();
            if (receiptTypeService.isHasteOutput(preRecepitType)) {
                if (preRecepitType.equals(EnumReceiptType.HASTE_STOCK_OUTPUT_PURCHASE_RETURN.getValue())) {
                    // 采购退货 放 退库临时区
                    // 入库退库临时存储区
                    typeCode = EnumDefaultStorageType.INPUT_RETURN.getTypeCode();
                    binCode = EnumDefaultStorageType.INPUT_RETURN.getBinCode();
                }
            } else if (receiptTypeService.isTempOutput(preRecepitType)) {
                if (preRecepitType.equals(EnumReceiptType.STOCK_OUTPUT_TEMP.getValue())) {
                    // 临时出库 放 出库临时区
                    // 出库临时存储区
                    typeCode = EnumDefaultStorageType.OUTPUT.getTypeCode();
                    binCode = EnumDefaultStorageType.OUTPUT.getBinCode();
                }
            } else if (receiptTypeService.getOutputReceiptType().contains(preRecepitType)) {
                if (preRecepitType.equals(EnumReceiptType.STOCK_OUTPUT_PURCHASE_RETURN.getValue())) {
                    // 采购退货 放 退库临时区
                    // 入库退库临时存储区
                    typeCode = EnumDefaultStorageType.INPUT_RETURN.getTypeCode();
                    binCode = EnumDefaultStorageType.INPUT_RETURN.getBinCode();
                }
            } else if (receiptTypeService.getTransportReceiptType().contains(preRecepitType)
                || EnumReceiptType.HASTE_STOCK_TRANSPORT.getValue().equals(preRecepitType)) {
                // 转储临时存储区(不区分正向与冲销)
                typeCode = EnumDefaultStorageType.TRANSPORT.getTypeCode();
                binCode = EnumDefaultStorageType.TRANSPORT.getBinCode();
            } else if (receiptTypeService.getInputReceiptType().contains(preRecepitType)
                || receiptTypeService.getReturnReceiptType().contains(preRecepitType)) {
                // 冲销临时存储区
                typeCode = EnumDefaultStorageType.WRITE_OFF.getTypeCode();
                binCode = EnumDefaultStorageType.WRITE_OFF.getBinCode();
            } else if (receiptTypeService.isHasteInput(preRecepitType)) {
                typeCode = EnumDefaultStorageType.WRITE_OFF.getTypeCode();
                binCode = EnumDefaultStorageType.WRITE_OFF.getBinCode();
            } else if (receiptTypeService.isTempInput(preRecepitType)) {
                typeCode = EnumDefaultStorageType.WRITE_OFF.getTypeCode();
                binCode = EnumDefaultStorageType.WRITE_OFF.getBinCode();
            }
        } else // 组盘上架作业
        if (EnumReceiptType.STOCK_TASK_SHELF_LOAD_SETUP.getValue().equals(receptType)) {
            if (receiptTypeService.isHasteInput(preRecepitType)) {
                typeCode = EnumDefaultStorageType.INPUT.getTypeCode();
                binCode = EnumDefaultStorageType.INPUT.getBinCode();
            } else if (receiptTypeService.isTempInput(preRecepitType)) {
                typeCode = EnumDefaultStorageType.INPUT.getTypeCode();
                binCode = EnumDefaultStorageType.INPUT.getBinCode();
            } else if (receiptTypeService.getInputReceiptType().contains(preRecepitType)) {
                // 入库临时存储区
                typeCode = EnumDefaultStorageType.INPUT.getTypeCode();
                binCode = EnumDefaultStorageType.INPUT.getBinCode();
            } else if (receiptTypeService.getReturnReceiptType().contains(preRecepitType)) {
                // 退库临时存储区
                typeCode = EnumDefaultStorageType.OUTPUT_RETURN.getTypeCode();
                binCode = EnumDefaultStorageType.OUTPUT_RETURN.getBinCode();
            } else if (receiptTypeService.getTransportReceiptType().contains(preRecepitType)
                || EnumReceiptType.HASTE_STOCK_TRANSPORT.getValue().equals(preRecepitType)) {
                // 转储临时存储区(不区分正向与冲销)
                typeCode = EnumDefaultStorageType.TRANSPORT.getTypeCode();
                binCode = EnumDefaultStorageType.TRANSPORT.getBinCode();
            } else if (receiptTypeService.getOutputReceiptType().contains(preRecepitType)) {
                // 冲销临时存储区
                typeCode = EnumDefaultStorageType.WRITE_OFF.getTypeCode();
                binCode = EnumDefaultStorageType.WRITE_OFF.getBinCode();
            } else if (receiptTypeService.isHasteInput(preRecepitType)) {
                // 冲销临时区
                typeCode = EnumDefaultStorageType.WRITE_OFF.getTypeCode();
                binCode = EnumDefaultStorageType.WRITE_OFF.getBinCode();
            } else if (receiptTypeService.isTempInput(preRecepitType)) {
                // 冲销临时区
                typeCode = EnumDefaultStorageType.WRITE_OFF.getTypeCode();
                binCode = EnumDefaultStorageType.WRITE_OFF.getBinCode();
            }
        }
        String whCode = Const.STRING_EMPTY;
        DicWh dicWh = dictionaryService.getWhCacheById(whId);
        if (null != dicWh) {
            whCode = dicWh.getWhCode();
        }
        // 存储类型id
        if (UtilString.isNotNullOrEmpty(whCode) && UtilString.isNotNullOrEmpty(typeCode)) {
            Long typeId = dictionaryService.getStorageTypeIdCacheByCode(whCode, typeCode);
            if (UtilNumber.isEmpty(typeId)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_STORAGE_WH_TYPE_NOT_EXIST, whCode, typeCode);
            }
            resultMap.put(TaskConst.TYPE_KEY_ID, typeId);
            resultMap.put(TaskConst.TYPE_KEY_CODE, typeCode);
        }
        // 仓位id
        if (UtilString.isNotNullOrEmpty(whCode) && UtilString.isNotNullOrEmpty(typeCode)
            && UtilString.isNotNullOrEmpty(binCode)) {
            Long binId = dictionaryService.getBinIdCacheByCode(whCode, typeCode, binCode);
            if (UtilNumber.isEmpty(binId)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_STORAGE_WH_TYPE_BIN_NOT_EXIST, whCode, typeCode,
                    binCode);
            }
            resultMap.put(TaskConst.BIN_KEY_ID, binId);
            resultMap.put(TaskConst.BIN_KEY_CODE, binCode);
        }
        return resultMap;
    }

    /**
     * 保存请求单据流
     *
     * @param receiptType 单据类型
     * @param itemList 行项目集合
     */
    void addReqReceiptTree(Integer receiptType, List<BizReceiptTaskReqItemDTO> itemList) {
        List<BizCommonReceiptRelation> dtoList = new ArrayList<>();
        for (BizReceiptTaskReqItemDTO item : itemList) {
            BizCommonReceiptRelation dto = new BizCommonReceiptRelation();
            dto.setReceiptType(receiptType);
            dto.setReceiptHeadId(item.getHeadId());
            dto.setReceiptItemId(item.getId());
            dto.setPreReceiptType(item.getPreReceiptType());
            dto.setPreReceiptHeadId(item.getPreReceiptHeadId());
            dto.setPreReceiptItemId(item.getPreReceiptItemId());
            dtoList.add(dto);
        }
        receiptRelationService.multiSaveReceiptTree(dtoList);
    }

    /**
     * 保存作业单单据流
     *
     * @param receiptType 单据类型
     * @param preReceiptType 前续单据类型
     * @param itemList 行项目集合
     */
    void addTaskReceiptTree(Integer receiptType, Integer preReceiptType, List<BizReceiptTaskItemDTO> itemList) {
        List<BizCommonReceiptRelation> dtoList = new ArrayList<>();
        for (BizReceiptTaskItemDTO item : itemList) {
            BizCommonReceiptRelation dto = new BizCommonReceiptRelation();
            dto.setReceiptType(receiptType);
            dto.setReceiptHeadId(item.getHeadId());
            dto.setReceiptItemId(item.getId());
            dto.setPreReceiptType(preReceiptType);
            dto.setPreReceiptHeadId(item.getTaskReqHeadId());
            dto.setPreReceiptItemId(item.getTaskReqItemId());
            dtoList.add(dto);
        }
        receiptRelationService.multiSaveReceiptTree(dtoList);
    }

    /**
     * 保存操作日志
     *
     * @param ctx 系统上下文
     */
    public void saveBizReceiptOperationLog(BizContext ctx) {
        // 入参上下文 - 保存的验收单
        BizReceiptTaskHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_REF_PO);
        // 保存操作日志
        receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(),
            EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT, "", ctx.getCurrentUser().getId());
    }

    /**
     * 设置行项目批次图片信息
     *
     * @in ctx 入参 {@link BizResultVO ("head":"作业请求详情")}
     * @out ctx 出参 {@link BizResultVO ("head":"作业请求详情及行项目批次图片")}
     */
    public void setBatchImg(BizContext ctx) {
        /* ************************ 获取上下文参数 **********************************/
        BizResultVO<BizReceiptTaskReqHeadDTO> headVo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        if (UtilObject.isNull(headVo.getHead()) || UtilCollection.isEmpty(headVo.getHead().getItemList())) {
            return;
        }
        // 获取请求head
        BizReceiptTaskReqHeadDTO headDTO = headVo.getHead();
        Set<Long> batchIdSet =
            headDTO.getItemList().stream().map(BizReceiptTaskReqItemDTO::getBatchId).collect(Collectors.toSet());
        // 获取批次图片
        Map<Long, List<BizBatchImgDTO>> imgMap = bizBatchImgService.getBatchImgListByBatchIdList(batchIdSet, 4);
        if (imgMap.isEmpty()) {
            return;
        }
        // 赋值批次图片
        headDTO.getItemList().forEach(item -> {
            if (UtilNumber.isNotEmpty(item.getBatchId()) && UtilCollection.isNotEmpty(imgMap.get(item.getBatchId()))
                && UtilObject.isNotNull(item.getBatchInfo())) {
                item.setBatchImgList(imgMap.get(item.getBatchId()));
            }
        });
        // 设置请求详情批次图片到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, headVo);
    }

    /**
     * 设置批次特性
     *
     * @in ctx 入参 {@link BizResultVO ("head":"作业请求详情")}
     * @out ctx 出参 {@link BizResultVO ("head":"作业请求详情及批次特性")}
     */
    public void setSpecFeature(BizContext ctx) {
        /* ************************ 获取上下文参数 **********************************/
        BizResultVO<BizReceiptTaskReqHeadDTO> headVo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        if (UtilObject.isNull(headVo.getHead()) || UtilCollection.isEmpty(headVo.getHead().getItemList())) {
            return;
        }
        // 获取验收特性
        // bizSpecFeatureValueService.getSpecList(headVo.getHead().getItemList(), BizReceiptTaskReqItemDTO.class, 1);
        // 获取物料特性
        // bizSpecFeatureValueService.getSpecList(headVo.getHead().getItemList(), BizReceiptTaskReqItemDTO.class, 2);
        // 设置采购验收单详情批次特性到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, headVo);
    }

    /**
     * 开启操作日志
     *
     * @in ctx 入参 {@link BizResultVO ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO ("extend":"扩展功能开启操作日志")}
     */
    public void setExtendOperationLog(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptTaskReqHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        resultVO.setExtend(new ExtendVO());
        // 设置操作日志开启/关闭
        resultVO.getExtend().setOperationLogRequired(UtilConst.getInstance().isOperationLogRequired());
    }

    /**
     * 根据前续单据集合删除请求行项目
     *
     * @param ctx 系统上下文
     * @throws WmsException 自定义异常
     */
    public void deleteReqItemByPreReceiptItemIds(BizContext ctx) throws WmsException {
        /* ************************ 获取上下文参数 **********************************/
        List<Long> preReceiptItemIdList = ctx.getContextData(Const.BIZ_CONTEXT_KEY_IDS);
        if (UtilCollection.isEmpty(preReceiptItemIdList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取前续单据行项目的head表的所有请求行项目
        List<BizReceiptTaskReqItem> receiptTaskReqItemList =
            bizReceiptTaskReqItemDataWrap.getBaseMapper().getReqItemListByPreReceiptItemIdList(preReceiptItemIdList);
        if (UtilCollection.isNotEmpty(receiptTaskReqItemList)) {
            List<Long> reqItemIds = new ArrayList<>();
            receiptTaskReqItemList.forEach(item -> {
                if (preReceiptItemIdList.contains(item.getPreReceiptItemId())) {
                    if (item.getItemStatus().equals(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue())) {
                        throw new WmsException(EnumReturnMsg.RETURN_CODE_REQ_ITEM_NOT_DELETE);
                    }
                    reqItemIds.add(item.getId());
                }
            });
            // 删除行项目
            bizReceiptTaskReqItemDataWrap.removeByIds(reqItemIds);
            // 如果请求的行项目=被删除的行项目数则删除请求head
            if (reqItemIds.size() == receiptTaskReqItemList.size()) {
                bizReceiptTaskReqHeadDataWrap.removeByIds(
                    receiptTaskReqItemList.stream().map(BizReceiptTaskReqItem::getHeadId).collect(Collectors.toList()));
            }
        }
    }

    /**
     * 根据前续单据集合修改请求行项目
     *
     * @param ctx 系统上下文
     * @throws WmsException 自定义异常
     */
    public void modifyReqItemByPreReceiptItemIds(BizContext ctx) throws WmsException {
        // 冲销的物料来源于同一个wh仓库
        // 1.将要冲销的行项目区分出是已记账（删除）/非已记账状态（修改）
        // 2.如果要冲销的的行项目只有已记账（删除）
        // 2.1.作业请求行项目表有做过上下架操作的行项目（taskqty>0代表操作过）
        // 2.1.1.如果操作过的（taskqty>0）行项目 ，删除要冲销的的行项目
        // 2.1.2.如果操作过的（taskqty>0）每一个行项目 itemstatus都是90，删除最后一个冲销的的行项目后，再看剩余作业请求行项目中是否还有taskqty=0的数据，如果没有
        // 将作业请求头项目状态改为已完成90
        // 2.2.作业请求行项目表没有做过上下架操作的行项目（所有的行项目taskqty=0）
        // 2.2.1.删除要冲销的的行项目，如果删除的数量=作业请求行项目表中数量，同时删除作业请求头表数据+单据流数据
        // 3.如果要冲销的的行项目只有非已记账状态（修改）
        // 3.1.作业请求行项目表有没做过上下架操作的行项目（存在行项目taskqty=0）
        // 3.1.1.只修改要冲销行项目的状态即可
        // 3.2.作业请求行项目表没有，没做过上下架操作的行项目（所有的行项目taskqty都>0）
        // 3.2.1.修改要冲销的行项目后，再看作业请求行项目表中是否itemstatus都是90，如果是将作业请求头项目状态改为已完成90
        // 4.要冲销的行项目两种状态都有
        // 4.1.删除所有要冲销的已记账的行项目
        // 4.2.修改所有要冲销的非已记账的行项目
        // 4.2.1.看作业请求行项目表中是否存在已记账（taskqty=0）的数据
        // 4.2.2.如果没有，看是否作业请求行项目表中是否存在taskqty>0 && itemstatus不为90,如果没有将作业请求头项目状态改为已完成90
        // 冲销的物料来源于同一个wh仓库，区分出仓库后循环以上操作
        /* ************************ 获取上下文参数 **********************************/
        List<Long> preReceiptItemIdList = ctx.getContextData(Const.BIZ_CONTEXT_KEY_IDS);
        if (UtilCollection.isEmpty(preReceiptItemIdList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取前续单据行项目的head表的所有请求行项目
        List<BizReceiptTaskReqItem> receiptTaskReqItemList =
            bizReceiptTaskReqItemDataWrap.getBaseMapper().getReqItemListByPreReceiptItemIdUseList(preReceiptItemIdList);
        // 请求headId集合
        Set<Long> receiptTaskReqHeadIdSet =
            receiptTaskReqItemList.stream().map(BizReceiptTaskReqItem::getHeadId).collect(Collectors.toSet());
        Map<Long, List<BizReceiptTaskReqItem>> HeadIdMap =
            receiptTaskReqItemList.stream().collect(Collectors.groupingBy(e -> e.getHeadId()));
        // 冲销的物料来源于同一个wh仓库
        if (receiptTaskReqHeadIdSet.size() == 1) {
            Long headId = new ArrayList<>(receiptTaskReqHeadIdSet).get(0);
            modifyReqItemByPreReceiptItemIdsOneWh(headId, preReceiptItemIdList, receiptTaskReqItemList);
        } else {
            // 冲销的物料来源于同一个wh仓库，区分出仓库后循环以上操作
            for (Map.Entry<Long, List<BizReceiptTaskReqItem>> m : HeadIdMap.entrySet()) {
                Long headId = m.getKey();
                List<BizReceiptTaskReqItem> list = m.getValue();
                // 将上下文中所有的冲销对象集合进行按自己对应仓库进行分装
                List<Long> preReceiptItemIdListUse = new ArrayList<>();
                for (Long item : preReceiptItemIdList) {
                    for (BizReceiptTaskReqItem obj : list) {
                        if (item.equals(obj.getPreReceiptItemId())) {
                            preReceiptItemIdListUse.add(obj.getPreReceiptItemId());
                        }
                    }
                }
                modifyReqItemByPreReceiptItemIdsOneWh(headId, preReceiptItemIdListUse, list);
            }
        }
    }

    /**
     * 冲销时修改一个wh仓库的作业请求单信息
     * 
     * @param headId 作业请求头表id
     * @param preReceiptItemIdList 页面勾选的要冲销的物料信息
     * @param receiptTaskReqItemList 作业请求行项目信息
     * @throws WmsException
     */
    public void modifyReqItemByPreReceiptItemIdsOneWh(Long headId, List<Long> preReceiptItemIdList,
        List<BizReceiptTaskReqItem> receiptTaskReqItemList) throws WmsException {
        // 1.将要冲销的行项目区分出是已记账（删除）/非已记账状态（修改）
        // 删除请求行项目id,已记账（删除)
        List<Long> reqItemDeleteIds = new ArrayList<>();
        // 修改请求行项目id,非已记账状态（修改）
        List<BizReceiptTaskReqItem> reqItemUpdateList = new ArrayList<>();
        for (Long preReceiptItemId : preReceiptItemIdList) {
            for (BizReceiptTaskReqItem item : receiptTaskReqItemList) {
                if (preReceiptItemId.equals(item.getPreReceiptItemId())) {
                    // 作业数量>0 ==》请求状态-已完成
                    if (item.getTaskQty().compareTo(BigDecimal.ZERO) > 0) {
                        item.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
                        reqItemUpdateList.add(item);
                    }
                    // 作业数量=0 ==》删除请求
                    if (item.getTaskQty().compareTo(BigDecimal.ZERO) == 0) {
                        reqItemDeleteIds.add(item.getId());
                    }
                }
            }
        }
        // 2.如果要冲销的的行项目只有已记账（删除）
        if (reqItemDeleteIds.size() > 0 && reqItemUpdateList.size() == 0) {
            // 判断是否有taskqty>0
            boolean flag = false;
            for (BizReceiptTaskReqItem reqItem : receiptTaskReqItemList) {
                if (reqItem.getTaskQty().compareTo(BigDecimal.ZERO) > 0) {
                    flag = true;
                    break;
                }
            }
            // 2.1.作业请求行项目表有做过上下架操作的行项目（taskqty>0代表操作过）
            if (flag) {
                // 2.1.1.如果操作过的（taskqty>0）行项目 ，删除要冲销的的行项目
                bizReceiptTaskReqItemDataWrap.removeByIds(reqItemDeleteIds);
                // 2.1.2.如果操作过的（taskqty>0）每一个行项目 itemstatus都是90，删除最后一个冲销的的行项目后，再看剩余作业请求行项目中是否还有taskqty=0的数据，如果没有
                // 将作业请求头项目状态改为已完成90
                QueryWrapper<BizReceiptTaskReqItem> queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda().eq(BizReceiptTaskReqItem::getHeadId, headId)
                    .eq(BizReceiptTaskReqItem::getIsDelete, 0);
                List<BizReceiptTaskReqItem> itemList = bizReceiptTaskReqItemDataWrap.list(queryWrapper);
                Integer taskCount = 0;
                Integer status90Count = 0;
                Integer task0Count = 0;
                for (BizReceiptTaskReqItem itemNew : itemList) {
                    if (itemNew.getTaskQty().compareTo(BigDecimal.ZERO) > 0) {
                        taskCount += 1;
                    }
                    if (itemNew.getTaskQty().compareTo(BigDecimal.ZERO) > 0
                        && EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(itemNew.getItemStatus())) {
                        status90Count += 1;
                    }
                    if (itemNew.getTaskQty().compareTo(BigDecimal.ZERO) == 0) {
                        task0Count += 1;
                    }
                }
                if (status90Count.equals(taskCount) && task0Count == 0) {
                    // 修改请求head状态-已完成
                    UpdateWrapper<BizReceiptTaskReqHead> wrapper = new UpdateWrapper<>();
                    wrapper.lambda()
                        .set(BizReceiptTaskReqHead::getReceiptStatus,
                            EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue())
                        .eq(BizReceiptTaskReqHead::getId, headId);
                    bizReceiptTaskReqHeadDataWrap.update(wrapper);
                }
            } else {
                // 2.2.作业请求行项目表没有做过上下架操作的行项目（所有的行项目taskqty=0）
                // 2.2.1.删除要冲销的的行项目，如果删除的数量=作业请求行项目表中数量，同时删除作业请求头表数据+单据流数据
                bizReceiptTaskReqItemDataWrap.removeByIds(reqItemDeleteIds);
                if (reqItemDeleteIds.size() == receiptTaskReqItemList.size()) {
                    BizReceiptTaskReqHead bizReceiptTaskReqHead = bizReceiptTaskReqHeadDataWrap.getById(headId);
                    bizReceiptTaskReqHeadDataWrap.removeById(headId);
                    // 删除单据流
                    receiptRelationService.deleteReceiptTree(bizReceiptTaskReqHead.getReceiptType(), headId);
                }
            }
        } else if (reqItemDeleteIds.size() == 0 && reqItemUpdateList.size() > 0) {
            // 3.如果要冲销的的行项目只有非已记账状态（修改）
            boolean flag = false;
            for (BizReceiptTaskReqItem reqItem : receiptTaskReqItemList) {
                if (reqItem.getTaskQty().compareTo(BigDecimal.ZERO) == 0) {
                    flag = true;
                    break;
                }
            }
            // 3.1.作业请求行项目表有没做过上下架操作的行项目（存在行项目taskqty=0）
            if (flag) {
                // 3.1.1.只修改要冲销行项目的状态即可
                bizReceiptTaskReqItemDataWrap.updateBatchDtoById(reqItemUpdateList);
            } else {
                bizReceiptTaskReqItemDataWrap.updateBatchDtoById(reqItemUpdateList);
                // 3.2.作业请求行项目表没有，没做过上下架操作的行项目（所有的行项目taskqty都>0）
                // 3.2.1.修改要冲销的行项目后，再看作业请求行项目表中是否itemstatus都是90，如果是将作业请求头项目状态改为已完成90
                QueryWrapper<BizReceiptTaskReqItem> queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda().eq(BizReceiptTaskReqItem::getHeadId, headId)
                    .eq(BizReceiptTaskReqItem::getIsDelete, 0);
                List<BizReceiptTaskReqItem> itemList = bizReceiptTaskReqItemDataWrap.list(queryWrapper);
                Integer status90Count = 0;
                for (BizReceiptTaskReqItem itemNew : itemList) {
                    if (itemNew.getTaskQty().compareTo(BigDecimal.ZERO) > 0
                        && EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(itemNew.getItemStatus())) {
                        status90Count += 1;
                    }
                }
                if (status90Count == itemList.size()) {
                    // 修改请求head状态-已完成
                    UpdateWrapper<BizReceiptTaskReqHead> wrapper = new UpdateWrapper<>();
                    wrapper.lambda()
                        .set(BizReceiptTaskReqHead::getReceiptStatus,
                            EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue())
                        .eq(BizReceiptTaskReqHead::getId, headId);
                    bizReceiptTaskReqHeadDataWrap.update(wrapper);
                }
            }
        } else if (reqItemDeleteIds.size() > 0 && reqItemUpdateList.size() > 0) {
            // 4.要冲销的行项目两种状态都有
            // 4.1.删除所有要冲销的已记账的行项目
            bizReceiptTaskReqItemDataWrap.removeByIds(reqItemDeleteIds);
            // 4.2.修改所有要冲销的非已记账的行项目
            bizReceiptTaskReqItemDataWrap.updateBatchDtoById(reqItemUpdateList);
            // 4.2.1.看作业请求行项目表中是否存在已记账（taskqty=0）的数据
            QueryWrapper<BizReceiptTaskReqItem> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(BizReceiptTaskReqItem::getHeadId, headId).eq(BizReceiptTaskReqItem::getIsDelete,
                0);
            List<BizReceiptTaskReqItem> itemList = bizReceiptTaskReqItemDataWrap.list(queryWrapper);
            Integer status85Count = 0;
            Integer task0Count = 0;
            for (BizReceiptTaskReqItem itemNew : itemList) {
                if (itemNew.getTaskQty().compareTo(BigDecimal.ZERO) == 0) {
                    task0Count += 1;
                }
                if (itemNew.getTaskQty().compareTo(BigDecimal.ZERO) > 0
                    && !EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(itemNew.getItemStatus())) {
                    status85Count += 1;
                }
            }
            // 4.2.2.如果没有，看是否作业请求行项目表中是否存在taskqty>0 && itemstatus不为90,如果没有将作业请求头项目状态改为已完成90
            if (task0Count == 0 && status85Count == 0) {
                // 修改请求head状态-已完成
                UpdateWrapper<BizReceiptTaskReqHead> wrapper = new UpdateWrapper<>();
                wrapper.lambda()
                    .set(BizReceiptTaskReqHead::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue())
                    .eq(BizReceiptTaskReqHead::getId, headId);
                bizReceiptTaskReqHeadDataWrap.update(wrapper);
            }
        }
    }

    /**
     * 根据前续单据行项目id获取作业集合
     *
     * @param preReceiptItemIdList 前续单据行项目id
     * @throws WmsException 自定义异常
     */
    public List<BizReceiptTaskItemDTO> getTaskItemByPreReceiptItemIds(List<Long> preReceiptItemIdList)
        throws WmsException {
        List<BizReceiptTaskItem> receiptTaskItemDTOList =
            bizReceiptTaskItemDataWrap.getBaseMapper().getTaskItemByPreReceiptItemIds(preReceiptItemIdList);
        List<BizReceiptTaskItemDTO> bizReceiptTaskItemDTOList =
            UtilCollection.toList(receiptTaskItemDTOList, BizReceiptTaskItemDTO.class);
        // 数据填充
        dataFillService.fillRlatAttrDataList(bizReceiptTaskItemDTOList);
        return bizReceiptTaskItemDTOList;
    }

    /* *************************************************组件*************************************************************/
    /**
     * 按钮组
     *
     * @param receiptStatus 单据状态
     * @return 按钮组对象
     */
    private ButtonVO setButton(Integer receiptStatus) {
        ButtonVO buttonVO = new ButtonVO();
        if (receiptStatus.equals(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue())) {
            // 草稿状态,按钮保存、提交、删除
            buttonVO.setButtonSave(true);
            buttonVO.setButtonSubmit(true);
            buttonVO.setButtonDelete(true);
        }

        return buttonVO;
    }

    /**
     * 扩展功能配置
     *
     * @param receiptStatus 单据状态
     * @return 扩展功能配置对象
     */
    private ExtendVO setExtend(Integer receiptStatus) {
        // TODO: 2021/3/23 extend 显示逻辑
        return null;
    }

    public void getUnLoadExportVo(BizContext ctx) {

        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(UtilExcel.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(UtilExcel.getFileName("下架请求单明细"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);
        BizResultVO vo= ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        BizReceiptTaskVO headVo= (BizReceiptTaskVO) vo.getHead();
        List<BizReceiptUnLoadTaskReqItemExportVO> list = UtilCollection.toList(headVo.getReqItemList(), BizReceiptUnLoadTaskReqItemExportVO.class);
        for (BizReceiptUnLoadTaskReqItemExportVO exportVo : list) {
            exportVo.setUnloadingRequestCode(headVo.getReceiptCode());
            exportVo.setReceiptTypeI18n(i18nTextCommonService.getNameMessage(UtilString.getLangCodeFromRequest(), "receiptType", headVo.getReceiptType().toString()));
            exportVo.setWhCodeAndWhName(exportVo.getWhCode()+"-"+exportVo.getWhName());
            exportVo.setBinCodeRecommend(Const.STRING_EMPTY);
            exportVo.setUnloadingRequestId(String.valueOf(headVo.getId()));
        }
        log.info("导出数据：{}", list);
        UtilExcel.writeExcel(BizReceiptUnLoadTaskReqItemExportVO.class, list, bizCommonFile);
        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    /**
     * 导出上架请求明细
     *
     * @param ctx
     */
    public void getLoadExportVo(BizContext ctx) {

        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(UtilExcel.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(UtilExcel.getFileName("上架请求单明细"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);
        BizResultVO vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        BizReceiptTaskVO headVo = (BizReceiptTaskVO) vo.getHead();
        List<BizReceiptLoadTaskReqItemExportVO> list = UtilCollection.toList(headVo.getReqItemList(), BizReceiptLoadTaskReqItemExportVO.class);
        for (BizReceiptLoadTaskReqItemExportVO exportVo : list) {
            exportVo.setReceiptCode(headVo.getReceiptCode());
            exportVo.setWhCodeAndWhName(exportVo.getWhCode() + "-" + exportVo.getWhName());
            exportVo.setBinCodeRecommend(Const.STRING_EMPTY);
            exportVo.setReceiptId(String.valueOf(headVo.getId()));
        }
        log.info("导出数据：{}", list);
        UtilExcel.writeExcel(BizReceiptLoadTaskReqItemExportVO.class, list, bizCommonFile);
        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    /**
     * 下架模板导入
     *
     * @param ctx
     */

    public void importUnLoadTaskReq(BizContext ctx, MultipartFile file) {
        try (InputStream inputStream = file.getInputStream()) {
            // 导入模板
            List<BizReceiptUnLoadTaskReqItemExportVO> importList = readExcelWithValidation(inputStream);
            // 校验
            this.validateUnloadImportData(importList);

            Map<String, List<BizReceiptUnLoadTaskReqItemExportVO>> listMap = importList.stream()
                    .collect(Collectors.groupingBy(BizReceiptUnLoadTaskReqItemExportVO::getRid));

            this.validateUnloadItemData(listMap);
            // 构造参数
            this.processUnLoadTaskItems(ctx, listMap);
        } catch (IOException e) {
            log.error("导入失败，文件：{}", file.getOriginalFilename(), e);
            throw new WmsException(EnumReturnMsg.RETURN_CODE_IO_EXCEPTION);
        }
    }

    private List<BizReceiptUnLoadTaskReqItemExportVO> readExcelWithValidation(InputStream inputStream) {
        try {
            return (List<BizReceiptUnLoadTaskReqItemExportVO>) UtilExcel.readExcelData(inputStream, BizReceiptUnLoadTaskReqItemExportVO.class, 1);
        } catch (ExcelAnalysisException e) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_IMOPRT_TEMPLATE_HAS_NO_DATA);
        } catch (Exception e) {
            log.error("Excel解析异常", e);
            if (e instanceof InterruptedException) {
                Thread.currentThread().interrupt();
            }
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EXCEPTION);
        }
    }

    private void validateUnloadImportData(List<BizReceiptUnLoadTaskReqItemExportVO> importList) {
        int rowNum = 1;
        for (BizReceiptUnLoadTaskReqItemExportVO dto : importList) {
            // if (UtilNumber.isEmpty(dto.getOperationQty())
            //         || UtilString.isNullOrEmpty(dto.getBatchCode())
            //         || UtilString.isNullOrEmpty(dto.getBinCodeRecommend())) {
            //     throw new WmsException(EnumReturnMsg.RETURN_CODE_EXCEL_REQUIRED_MSG, String.valueOf(rowNum));
            // }
            if (UtilNumber.isEmpty(dto.getOperationQty())) {
                dto.setOperationQty(BigDecimal.ZERO);
            }
            if (UtilString.isNotNullOrEmpty(dto.getBinCodeRecommend())) {
                if (Objects.isNull(dictionaryService.getBinCacheByCode(dto.getBinCodeRecommend()))) {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_BIN_ERROR);
                }
            }

            rowNum++;
        }
    }

    public void validateUnloadItemData(Map<String, List<BizReceiptUnLoadTaskReqItemExportVO>> listMap) {
        listMap.forEach((rid, list) -> {
            BigDecimal sumQty = list.stream()
                    .map(BizReceiptUnLoadTaskReqItemExportVO::getOperationQty)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            if (list.isEmpty() || sumQty.compareTo(list.get(0).getUnTaskQty()) > 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_QTY_GT_UNLOAD_QTY, rid);
            }
        });
    }

    private void processUnLoadTaskItems(BizContext ctx, Map<String, List<BizReceiptUnLoadTaskReqItemExportVO>> listMap) {


        BizResultVO<BizReceiptTaskVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        BizReceiptTaskVO headDTO = vo.getHead();
        List<BizReceiptTaskReqItemVO> taskReqItemDTOS = headDTO.getReqItemList();

        taskReqItemDTOS.forEach(item -> processUnloadItem(listMap, item));

        headDTO.setReqItemList(taskReqItemDTOS);
        // po.setStockTaskReqHeadInfo(headDTO);

        BizResultVO<BizReceiptTaskVO> bizResultVO = new BizResultVO<>();
        bizResultVO.setHead(headDTO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, bizResultVO);
    }

    private List<BizReceiptTaskReqItemDTO> convertToDTOList(BizResultVO<BizReceiptTaskVO> vo) {
        return UtilCollection.toList(
                vo.getHead().getReqItemList(),
                BizReceiptTaskReqItemDTO.class
        );
    }

    private void processUnloadItem(Map<String, List<BizReceiptUnLoadTaskReqItemExportVO>> listMap, BizReceiptTaskReqItemVO item) {
        Optional.ofNullable(listMap.get(item.getRid()))
                .filter(UtilCollection::isNotEmpty)
                .ifPresent(exportVos -> {
                    BigDecimal totalQty = exportVos.stream()
                            .map(BizReceiptUnLoadTaskReqItemExportVO::getOperationQty)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    // item.setQty(totalQty);
                    item.setSubmitQty(totalQty);
                    item.setStockBinList(createUnloadStockBinList(item.getStockBinList(), exportVos));
                });
    }

    private List<RecommendStockBinVO> createUnloadStockBinList(List<RecommendStockBinVO> stockBinVOList, List<BizReceiptUnLoadTaskReqItemExportVO> exportVos) {
        // return exportVos.stream().map(vo -> {
        //     BizBatchInfoDTO batch = Optional.ofNullable(batchInfoService.getBatchInfoDtoByCode(vo.getBatchCode()))
        //             .orElseThrow(() -> new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR_WITH_DESC, vo.getBatchCode()));
        //
        //     DicWhStorageBinDTO binCache = Optional.ofNullable(dictionaryService.getBinCacheByCode(vo.getBinCodeRecommend()))
        //             .orElseThrow(() -> new WmsException(EnumReturnMsg.RETURN_CODE_STORAGE_BIN_NOT_EXIST));
        //
        //     return new RecommendStockBinVO()
        //             .setMatId(batch.getMatId())
        //             .setWhCode(binCache.getWhCode())
        //             .setLabelDataList(new ArrayList<>())
        //             .setTypeId(binCache.getTypeId())
        //             .setBatchId(batch.getId())
        //             .setBatchInfo(batch)
        //             .setQty(vo.getOperationQty())
        //             .setOperationQty(vo.getOperationQty())
        //             .setBinCode(vo.getBinCodeRecommend())
        //             .setBinId(binCache.getId());
        // }).collect(Collectors.toList());
        List<RecommendStockBinVO> result = new ArrayList<>();
        stockBinVOList.forEach(bin -> {
            exportVos.forEach(vo -> {
                if (bin.getBatchInfo().getBatchCode().equals(vo.getBatchCode()) && bin.getBinCode().equals(vo.getBinCodeRecommend())) {
                    bin.setQty(vo.getOperationQty());
                    bin.setOperationQty(vo.getOperationQty());
                    result.add(bin);
                }
            });
        });
        return result;
    }


    /**
     * 自定义返回结构
     *
     * @param ctx 系统上下文
     */
    public void customizeResult(BizContext ctx) {
        /* ************************ 获取上下文参数 **********************************/
        BizResultVO<BizReceiptTaskReqHeadDTO> headVo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        if (null == headVo) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 上架
        if (EnumReceiptType.STOCK_TASK_REQ_SHELF_LOAD.getValue().equals(headVo.getHead().getReceiptType())
            || EnumReceiptType.UNITIZED_STOCK_TASK_REQ_SHELF_LOAD.getValue().equals(headVo.getHead().getReceiptType())) {
            this.customizeLoadResult(ctx);
        } else // 下架
        if (EnumReceiptType.STOCK_TASK_REQ_SHELF_UNLOAD.getValue().equals(headVo.getHead().getReceiptType()) ||
                EnumReceiptType.UNITIZED_STOCK_TASK_REQ_SHELF_UNLOAD.getValue().equals(headVo.getHead().getReceiptType())) {
            this.customizeUnLoadResult(ctx);
        }
    }

    /**
     * 自定义上架返回结果
     *
     * @param ctx 系统上下文
     */
    private void customizeLoadResult(BizContext ctx) {
        /* ************************ 获取上下文参数 **********************************/
        List<BizReceiptTaskItemDTO> taskItemDTOList = ctx.getContextData(Const.BIZ_CONTEXT_KEY_REF_PO);
        BizResultVO<BizReceiptTaskReqHeadDTO> headVo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 请求head id
        if(UtilCollection.isEmpty(headVo.getHead().getItemList())){
            return;
        }
        Long receiptHeadId = headVo.getHead().getId();
        // 自定义返回结构
        BizReceiptTaskVO vo = UtilBean.newInstance(headVo.getHead(), BizReceiptTaskVO.class);
        // 请求行项目map（作业单获取请求行项目的信息）
        Map<Long, BizReceiptTaskReqItemDTO> taskReqItemDTOMap = new HashMap<>();
        // 请求标签集合
        List<BizLabelReceiptRelDTO> bizReqLabelReceiptRelDTOList =
            labelReceiptRelService.getDTOList(new ArrayList<Long>() {

                {
                    add(receiptHeadId);
                }
            }, null, null, null);
        Map<Long,
            List<BizLabelReceiptRelDTO>> relReqItemMap = bizReqLabelReceiptRelDTOList.stream()
                .filter(item -> item.getStatus().equals(EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue()))
                .collect(Collectors.groupingBy(BizLabelReceiptRelDTO::getReceiptItemId));
        headVo.getHead().getItemList().forEach(reqItem -> {
            reqItem.setLabelReceiptRelDTOList(relReqItemMap.get(reqItem.getId()));
            taskReqItemDTOMap.put(reqItem.getId(), reqItem);
        });
        /* ************************ 组装返回结构 **********************************/
        // reqItemList
        List<BizReceiptTaskReqItemDTO> itemDTOList = headVo.getHead().getItemList();
        // 转vo
        List<BizReceiptTaskReqItemVO> itemVOList = new ArrayList<>();
        itemDTOList.forEach(item -> {
            BizReceiptTaskReqItemVO itemVO = UtilBean.deepCopyNewInstance(item, BizReceiptTaskReqItemVO.class);
            itemVO.setMatCode(item.getMatInfo().getMatCode());
            itemVO.setMatName(item.getMatInfo().getMatName());
            itemVO.setMatNameEn(item.getMatInfo().getMatNameEn());
            itemVO.setExtManufacturerPartNumber(item.getMatInfo().getExtManufacturerPartNumber());
            itemVO.setExtMainMaterial(item.getMatInfo().getExtMainMaterial());
            itemVO.setExtIndustryStandardDesc(item.getMatInfo().getExtIndustryStandardDesc());
            itemVO.setNetWeight(item.getMatInfo().getNetWeight());
            itemVO.setWeightTolerance(item.getMatInfo().getWeightTolerance());
            itemVO.setUnTaskQty(item.getQty().subtract(item.getTaskQty()));
            DicWhStorageBinDTO recommendBin = itemVO.getStorageBin();
            if(recommendBin != null ){
                itemVO.setBinCode(recommendBin.getBinCode());
                itemVO.setTypeCode(recommendBin.getTypeCode());
                itemVO.setTypeName(recommendBin.getTypeName());
                itemVO.setTypeId(recommendBin.getTypeId());
            }else{
                itemVO.setStorageBin(new DicWhStorageBinDTO());
            }
            DicMaterialFactoryDTO dicMaterialFactory = dictionaryService.getDicMaterialFactoryByUniqueKey(itemVO.getMatId(), itemVO.getFtyId());
            itemVO.setShelfLifeMax(UtilObject.isNotNull(dicMaterialFactory) ? dicMaterialFactory.getShelfLifeMax() : 0);
            itemVO.setShelfLifeMin(UtilObject.isNotNull(dicMaterialFactory) ? dicMaterialFactory.getShelfLifeMin() : 0);
            itemVO.setPackageType(UtilObject.isNotNull(dicMaterialFactory) ? dicMaterialFactory.getPackageType() : 0);
            itemVO.setDepositType(UtilObject.isNotNull(dicMaterialFactory) ? dicMaterialFactory.getDepositType() : 0);
            itemVO.setMatGroupCode(item.getMatGroupCode());
            itemVO.setMatGroupName(item.getMatGroupName());
            itemVO.setMatGroupId(item.getMatGroupId());
            // 验收入库-前置单据是:有条件放行, 则只能选择冻结存储区
            if(UtilNumber.isNotEmpty(item.getPreReceiptType()) && item.getPreReceiptType().equals(EnumReceiptType.UNITIZED_STOCK_INPUT_INSPECT.getValue())){
                BizReceiptInputItem bizReceiptInputItem = bizReceiptInputItemDataWrap.getOne(new QueryWrapper<BizReceiptInputItem>().lambda().eq(BizReceiptInputItem::getHeadId, item.getPreReceiptHeadId()).last("limit 1"));
                if(bizReceiptInputItem.getPreReceiptType().equals(EnumReceiptType.UNITIZED_CONDITIONAL_RELEASE.getValue())){
                    itemVO.setStockStatus(EnumStockStatus.STOCK_BATCH_STATUS_FREEZE.getValue());
                }
            }
            itemVO.setSubmitQty(itemVO.getQty());
            itemVOList.add(itemVO);
        });
        // 过滤掉已完成的请求行项目
        vo.setReqItemList(itemVOList.stream().filter(itemVo -> !EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(itemVo.getItemStatus())).collect(Collectors.toList()));
        //vo.setReqItemList(itemVOList);

        // 拼装taskItemList
        List<BizReceiptTaskItemVO> taskItemVoList = new ArrayList<>();
        if (UtilCollection.isNotEmpty(taskItemDTOList)) {
            // 作业head id
            List<Long> taskHeadIdList =
                taskItemDTOList.stream().map(BizReceiptTaskItemDTO::getHeadId).collect(Collectors.toList());
            // 作业单head集合
            List<BizReceiptTaskHeadVO> bizReceiptTaskHeadVOList =
                UtilCollection.toList(bizReceiptTaskHeadDataWrap.listByIds(taskHeadIdList), BizReceiptTaskHeadVO.class);
            // 数据填充
            dataFillService.fillRlatAttrDataList(bizReceiptTaskHeadVOList);
            Map<Long, BizReceiptTaskHeadVO> receiptTaskHeadVOMap = bizReceiptTaskHeadVOList.stream().collect(Collectors
                .groupingBy(BizReceiptTaskHeadVO::getId, Collectors.collectingAndThen(Collectors.toList(), value -> {
                    value.get(0).setBizReceiptTaskItemVOList(new ArrayList<>());
                    return value.get(0);
                })));
            // 作业单标签集合
            List<BizLabelReceiptRelDTO> bizTaskLabelReceiptRelDTOList =
                labelReceiptRelService.getDTOList(taskHeadIdList, null, null, null);
            Map<Long, List<BizLabelReceiptRelDTO>> relTaskItemMap = bizTaskLabelReceiptRelDTOList.stream()
                .collect(Collectors.groupingBy(BizLabelReceiptRelDTO::getReceiptItemId));
            // 请求行项目信息
            BizReceiptTaskReqItemDTO reqItemDTO;
            for (BizReceiptTaskItemDTO itemDTO : taskItemDTOList) {
                BizReceiptTaskItemVO taskItem = UtilBean.newInstance(itemDTO, BizReceiptTaskItemVO.class);
                reqItemDTO = taskReqItemDTOMap.get(itemDTO.getTaskReqItemId());
                if (null == reqItemDTO) {
                    continue;
                }
                // 根据请求行项目属性赋值作业单行项目
                taskItem.setMatCode(reqItemDTO.getMatInfo().getMatCode());
                taskItem.setMatName(reqItemDTO.getMatInfo().getMatName());
                taskItem.setMatNameEn(reqItemDTO.getMatInfo().getMatNameEn());
                taskItem.setExtManufacturerPartNumber(reqItemDTO.getMatInfo().getExtManufacturerPartNumber());
                taskItem.setExtMainMaterial(reqItemDTO.getMatInfo().getExtMainMaterial());
                taskItem.setExtIndustryStandardDesc(reqItemDTO.getMatInfo().getExtIndustryStandardDesc());
                taskItem.setUnitCode(reqItemDTO.getUnitCode());
                taskItem.setUnitName(reqItemDTO.getUnitName());
                taskItem.setDecimalPlace(reqItemDTO.getDecimalPlace());
                taskItem.setLabelReceiptRelDTOList(relTaskItemMap.get(itemDTO.getId()));
                DicMaterialFactoryDTO dicMaterialFactory = dictionaryService.getDicMaterialFactoryByUniqueKey(itemDTO.getMatId(), itemDTO.getFtyId());
                taskItem.setShelfLifeMax(UtilObject.isNotNull(dicMaterialFactory) ? dicMaterialFactory.getShelfLifeMax() : 0);
                taskItem.setShelfLifeMin(UtilObject.isNotNull(dicMaterialFactory) ? dicMaterialFactory.getShelfLifeMin() : 0);
                taskItem.setPackageType(UtilObject.isNotNull(dicMaterialFactory) ? dicMaterialFactory.getPackageType() : 0);
                taskItem.setDepositType(UtilObject.isNotNull(dicMaterialFactory) ? dicMaterialFactory.getDepositType() : 0);
                taskItem.setMatGroupCode(reqItemDTO.getMatGroupCode());
                taskItem.setMatGroupName(reqItemDTO.getMatGroupName());
                taskItem.setMatGroupId(reqItemDTO.getMatGroupId());
                receiptTaskHeadVOMap.get(itemDTO.getHeadId()).getBizReceiptTaskItemVOList().add(taskItem);
                taskItemVoList.add(taskItem);
            }
            vo.setTaskHeadList(new ArrayList<>(receiptTaskHeadVOMap.values()));
            vo.setTaskItemList(JSONObject.parseArray(
                JSON.toJSONString(taskItemVoList, SerializerFeature.DisableCircularReferenceDetect),
                BizReceiptTaskItemVO.class));
        }
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(vo, headVo.getExtend(), headVo.getButton()));
    }

    /**
     * 自定义下架返回结果
     *
     * @param ctx 系统上下文
     */
    private void customizeUnLoadResult(BizContext ctx) {
        /* ************************ 获取上下文参数 **********************************/
        List<BizReceiptTaskItemDTO> taskItemDTOList = ctx.getContextData(Const.BIZ_CONTEXT_KEY_REF_PO);
        BizResultVO<BizReceiptTaskReqHeadDTO> headVo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 自定义返回结构
        BizReceiptTaskVO vo = UtilBean.newInstance(headVo.getHead(), BizReceiptTaskVO.class);
        /* ************************ 组装返回结构 **********************************/
        // reqItemList
        List<BizReceiptTaskReqItemVO> reqItemVOList = new ArrayList<>();
        // 请求行项目map（作业单获取请求行项目的信息）
        Map<Long, BizReceiptTaskReqItemDTO> taskReqItemDTOMap = new HashMap<>();
        boolean recommendFlag = false;
        List<BizReceiptTaskReqItemDTO> reqItemDTOList = headVo.getHead().getItemList();
        for (int i = 0; i < reqItemDTOList.size(); i++) {
            BizReceiptTaskReqItemDTO reqItem = reqItemDTOList.get(i);
            if (i == 0) {
                Integer preReceiptType = reqItemDTOList.get(0).getPreReceiptType();
                if (preReceiptType.equals(EnumReceiptType.STOCK_OUTPUT_MAT_REQ.getValue()) || preReceiptType.equals(EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ.getValue()) ) {
                    recommendFlag = true;
                }
            }
            BigDecimal unTaskQty = reqItem.getQty().subtract(reqItem.getTaskQty());
            boolean openRecommend = true;
            BizReceiptTaskReqItemVO reqItemVO = UtilBean.deepCopyNewInstance(reqItem, BizReceiptTaskReqItemVO.class);
            List<StockBinDTO> stockBinDTOList = new ArrayList<>();
            if(UtilNumber.isNotEmpty(reqItem.getReferReceiptType()) && reqItem.getReferReceiptType().equals(EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ_APPLY_BY_REQUIRE.getValue())){
                // 根据出库申请中选择的库存过滤仓位库存
                stockBinDTOList = stockCommonService.getStockBinByApply(reqItem);

            } else {
                stockBinDTOList = stockCommonService.getStockBinByFeatureCodeAndValue(reqItem,
                        reqItem.getFtyId(), reqItem.getLocationId(), reqItem.getMatId(),
                        stockCommonService.getStockStatus(reqItem.getPreReceiptType()), reqItem.getSpecStock());
            }
            if (recommendFlag) {
                // 先进先出规则
                stockBinDTOList = stockBinDTOList.stream().sorted(Comparator.comparing(StockBinDTO::getInputDate, Comparator.nullsLast(Comparator.naturalOrder()))).collect(Collectors.toList());
            }
            log.info("请求行项目 itemId：{}，库存 stockBinVOList：{}", reqItem.getId(), stockBinDTOList);
            //如果是报废出库生成的下架请求，请求可用数量要使用freezeqty，否则前台不能选择数量下架
            List<RecommendStockBinVO> stockBinVOList = new ArrayList<>();
            if (UtilCollection.isNotEmpty(stockBinDTOList)) {
                for (StockBinDTO binDTO : stockBinDTOList) {
                    RecommendStockBinVO binVO = UtilBean.newInstance(binDTO, RecommendStockBinVO.class);
                    List<BizLabelDataDTO> labelDTOList =
                            UtilCollection.toList(binDTO.getLabelDataList(), BizLabelDataDTO.class);
                    if (recommendFlag && openRecommend) {
                        Integer trueFlag = EnumRealYn.TRUE.getIntValue();
                        BigDecimal stockQty = binVO.getStockQty();
                        binVO.setRecommendFlag(trueFlag);
                        if (unTaskQty.compareTo(stockQty) <= 0) {
                            openRecommend = false;
                            binVO.setOperationQty(unTaskQty);
                        } else {
                            binVO.setOperationQty(stockQty);
                            unTaskQty = unTaskQty.subtract(stockQty);
                        }
                        BigDecimal operationQty = binVO.getOperationQty();
                        for (BizLabelDataDTO labelDataDTO : labelDTOList) {
                            BigDecimal labelQty = labelDataDTO.getQty();
                            if (labelQty.compareTo(BigDecimal.ZERO) == 0) {
                                continue;
                            }
                            labelDataDTO.setRecommendFlag(trueFlag);
                            if (operationQty.compareTo(labelQty) <= 0) {
                                labelDataDTO.setOperationQty(operationQty);
                                break;
                            } else {
                                labelDataDTO.setOperationQty(labelQty);
                                operationQty = operationQty.subtract(labelQty);
                            }
                        }
                    }
                    binVO.setLabelDataList(labelDTOList);
                    stockBinVOList.add(binVO);
                }
                reqItemVO.setBinCodeRecommend(stockBinVOList.get(0).getBinCode());
            } else if (recommendFlag) {
                reqItemVO.setBinCodeRecommend("");
            }
            UtilCollection.toList(stockBinDTOList, RecommendStockBinVO.class);
            // 是否启用下架策略
            if (UtilConst.getInstance().getUnloadStrategyEnabled()) {
                /* ************************ 组装推荐仓位参数 **********************************/
                // 物料的仓位库存
                ctx.setContextData("stockBinList", stockBinVOList);
                // 请求行项目
                ctx.setContextData("reqItem", reqItem);
                // 推荐仓位
                Map<Long, List<RecommendStockBinVO>> recommendResult = UnloadRecommendService.recommend(ctx);
                if (!recommendResult.isEmpty()) {
                    reqItemVO.setStockBinList(recommendResult.get(reqItem.getMatId()));
                }
            } else {
                reqItemVO.setStockBinList(stockBinVOList);
            }
            if (UtilObject.isNotEmpty(reqItem.getMatInfo())) {
                // 物料编码
                reqItemVO.setMatCode(reqItem.getMatInfo().getMatCode());
                // 物料描述
                reqItemVO.setMatName(reqItem.getMatInfo().getMatName());
                reqItemVO.setMatNameEn(reqItem.getMatInfo().getMatNameEn());
                reqItemVO.setExtManufacturerPartNumber(reqItem.getMatInfo().getExtManufacturerPartNumber());
                reqItemVO.setExtMainMaterial(reqItem.getMatInfo().getExtMainMaterial());
                reqItemVO.setExtIndustryStandardDesc(reqItem.getMatInfo().getExtIndustryStandardDesc());
                reqItemVO.setNetWeight(reqItem.getMatInfo().getNetWeight());
                reqItemVO.setWeightTolerance(reqItem.getMatInfo().getWeightTolerance());
            }
            reqItemVO.setUnTaskQty(reqItem.getQty().subtract(reqItem.getTaskQty()));
            reqItemVOList.add(reqItemVO);
            taskReqItemDTOMap.put(reqItem.getId(), reqItem);
        }
        // 过滤掉已完成的请求行项目
        List<BizReceiptTaskReqItemVO> reqItemList = reqItemVOList.stream().filter(itemVo -> !EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(itemVo.getItemStatus())).collect(Collectors.toList());
        if (recommendFlag) {
            reqItemList.sort(Comparator.comparing(BizReceiptTaskReqItemVO::getBinCodeRecommend));
        }
        vo.setReqItemList(reqItemList);
        //vo.setReqItemList(reqItemVOList);
        // 拼装taskItemList
        List<BizReceiptTaskItemVO> taskItemVoList = new ArrayList<>();
        if (UtilCollection.isNotEmpty(taskItemDTOList)) {
            // 作业head id
            List<Long> taskHeadIdList =
                taskItemDTOList.stream().map(BizReceiptTaskItemDTO::getHeadId).collect(Collectors.toList());
            // 作业单head集合
            List<BizReceiptTaskHeadVO> bizReceiptTaskHeadVOList =
                UtilCollection.toList(bizReceiptTaskHeadDataWrap.listByIds(taskHeadIdList), BizReceiptTaskHeadVO.class);
            // 数据填充
            dataFillService.fillRlatAttrDataList(bizReceiptTaskHeadVOList);
            Map<Long, BizReceiptTaskHeadVO> receiptTaskHeadVOMap = bizReceiptTaskHeadVOList.stream().collect(Collectors
                .groupingBy(BizReceiptTaskHeadVO::getId, Collectors.collectingAndThen(Collectors.toList(), value -> {
                    value.get(0).setBizReceiptTaskItemVOList(new ArrayList<>());
                    return value.get(0);
                })));
            // 作业单标签集合
            List<BizLabelReceiptRelDTO> bizTaskLabelReceiptRelDTOList =
                labelReceiptRelService.getDTOList(taskHeadIdList, null, null, null);
            Map<Long, List<BizLabelReceiptRelDTO>> relTaskItemMap = bizTaskLabelReceiptRelDTOList.stream()
                .collect(Collectors.groupingBy(BizLabelReceiptRelDTO::getReceiptItemId));
            // 请求行项目信息
            BizReceiptTaskReqItemDTO reqItemDTO;
            for (BizReceiptTaskItemDTO itemDTO : taskItemDTOList) {
                BizReceiptTaskItemVO taskItem = UtilBean.newInstance(itemDTO, BizReceiptTaskItemVO.class);
                reqItemDTO = taskReqItemDTOMap.get(itemDTO.getTaskReqItemId());
                if (null == reqItemDTO) {
                    continue;
                }
                taskItem.setPreReceiptCode(reqItemDTO.getPreReceiptCode());
                // 根据请求行项目属性赋值作业单行项目
                DicMaterialDTO matInfo = dictionaryService.getMatCacheById(itemDTO.getMatId());
                DicUnit dicUnit = dictionaryService.getUnitCacheById(matInfo.getUnitId());
                taskItem.setMatCode(matInfo.getMatCode());
                taskItem.setMatName(matInfo.getMatName());
                taskItem.setMatNameEn(matInfo.getMatNameEn());
                taskItem.setExtManufacturerPartNumber(matInfo.getExtManufacturerPartNumber());
                taskItem.setExtMainMaterial(matInfo.getExtMainMaterial());
                taskItem.setExtIndustryStandardDesc(matInfo.getExtIndustryStandardDesc());
                taskItem.setUnitCode(matInfo.getUnitCode());
                taskItem.setUnitName(matInfo.getUnitName());
                taskItem.setDecimalPlace(dicUnit.getDecimalPlace());
                taskItem.setLabelReceiptRelDTOList(relTaskItemMap.get(itemDTO.getId()));
                receiptTaskHeadVOMap.get(itemDTO.getHeadId()).getBizReceiptTaskItemVOList().add(taskItem);
                taskItemVoList.add(taskItem);
            }
            vo.setTaskHeadList(new ArrayList<>(receiptTaskHeadVOMap.values()));
            vo.setTaskItemList(JSONObject.parseArray(
                JSON.toJSONString(taskItemVoList, SerializerFeature.DisableCircularReferenceDetect),
                BizReceiptTaskItemVO.class));
        }
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(vo, headVo.getExtend(), headVo.getButton()));
    }

    /**
     * 保存操作日志
     *
     * @param ctx 系统上下文
     */
    public void saveBizReceiptOperationLog(BizContext ctx, EnumReceiptOperationType receiptOperationType) {
        // 入参上下文 - 保存的验收单
        BizReceiptTaskHead headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_HEAD_VO);
        // 保存操作日志
        receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(), receiptOperationType, "", ctx.getCurrentUser().getId());
    }

    /**
     * 下架作业单撤回校验
     *
     * @param ctx 系统上下文
     */
    public void checkUnLoadTaskRollback(BizContext ctx) {
        log.info("下架作业单撤回数据校验 ctx：{}", JSONObject.toJSONString(ctx));
        BizReceiptTaskRollbackPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (null == po) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 仅领料出库的下架作业单支持撤回
        Long reqItemId = po.getTaskReqItemId();
        BizReceiptTaskReqItem taskReqItem = bizReceiptTaskReqItemDataWrap.getById(reqItemId);
        int preReceiptType = taskReqItem.getPreReceiptType();
        if (! (EnumReceiptType.STOCK_OUTPUT_MAT_REQ.getValue().equals(preReceiptType)
        ||EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ.getValue().equals(preReceiptType) ) ) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_TYPE_FORBID);
        }
        Long outputHeadId = taskReqItem.getPreReceiptHeadId();
        if (UtilNumber.isEmpty(outputHeadId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        BizReceiptOutputHead bizReceiptOutputHead = bizReceiptOutputHeadDataWrap.getById(outputHeadId);
        if (bizReceiptOutputHead == null) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        Long reqHeadId = po.getTaskReqHeadId();
        BizReceiptTaskReqHead taskReqHead = bizReceiptTaskReqHeadDataWrap.getById(reqHeadId);
        if (taskReqHead == null) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 校验出库单状态, 只有作业中、已作业和未同步可以撤销
        Integer receiptStatus = bizReceiptOutputHead.getReceiptStatus();
        Integer inTaskStatus = EnumReceiptStatus.RECEIPT_STATUS_IN_TASK.getValue();
        Integer taskStatus = EnumReceiptStatus.RECEIPT_STATUS_TASK.getValue();
        Integer unSyncStatus = EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue();
        if (!receiptStatus.equals(inTaskStatus) && !receiptStatus.equals(taskStatus) && !receiptStatus.equals(unSyncStatus)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_STATUS_FALSE);
        }
        Long taskHeadId = po.getTaskHeadId();
        BizReceiptTaskHead taskHead = bizReceiptTaskHeadDataWrap.getById(taskHeadId);
        if (taskHead == null) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        List<BizReceiptTaskItem> taskItemList = bizReceiptTaskItemDataWrap.findByHead(taskHeadId);
        if (UtilCollection.isEmpty(taskItemList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        Long taskItemId = po.getTaskItemId();
        BizReceiptTaskItem taskItem = null;
        for (BizReceiptTaskItem bizReceiptTaskItem : taskItemList) {
            if (taskItemId.equals(bizReceiptTaskItem.getId())) {
                taskItem = bizReceiptTaskItem;
                break;
            }
        }
        if (taskItem == null) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        BigDecimal rollbackQty = taskItem.getQty();
        BigDecimal taskQty = taskReqItem.getTaskQty();
        BigDecimal remainTaskQty = taskQty.subtract(rollbackQty);
        if (remainTaskQty.compareTo(BigDecimal.ZERO) < 0) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, taskItem);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_HEAD_VO, taskHead);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_REF_PO, taskReqItem);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_REF_HEAD_PO, taskReqHead);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_BIN_VO, bizReceiptOutputHead);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO_LIST, taskItemList);
    }

    public void genUnLoadTaskRollbackInsDoc(BizContext ctx) {
        log.info("生成ins凭证 ctx：{}", JSONObject.toJSONString(ctx));
        /* ************************ 获取上下文参数 **********************************/
        BizReceiptTaskItem taskItem = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        BizReceiptTaskHead taskHead = ctx.getContextData(Const.BIZ_CONTEXT_KEY_HEAD_VO);
        BizReceiptTaskReqItem taskReqItem = ctx.getContextData(Const.BIZ_CONTEXT_KEY_REF_PO);
        List<BizLabelReceiptRel> receiptRelList = ctx.getContextData(Const.BIZ_CONTEXT_KEY_LABEL_LIST);
        // 凭证
        StockInsMoveTypeDTO insMoveTypeDTO = taskMoveTypeComponent.genUnLoadTaskRollbackInsDoc(taskHead, taskItem, taskReqItem, receiptRelList);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_INS, insMoveTypeDTO);
    }

    /**
     * 作业请求数据校验
     *
     * @param ctx 系统上下文
     */
    public void CheckSaveDataUnLoad(BizContext ctx) {
        log.info("作业请求数据校验 ctx：{}", JSONObject.toJSONString(ctx));
        /* ************************ 获取上下文参数 **********************************/
        BizReceiptTaskReqSavePo po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        /* ************************ 空行项目校验 **********************************/
        if (null == po) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        if (UtilCollection.isEmpty(po.getStockTaskReqHeadInfo().getItemList())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
        /* ************************ 请求行项目操作数量为0校验，去除操作数量为0的行项目 **********************************/
        // 行项目
        List<BizReceiptTaskReqItemDTO> stockTaskReqItemDTOList = po.getStockTaskReqHeadInfo().getItemList();
        stockTaskReqItemDTOList.removeIf(taskReqItem -> taskReqItem.getSubmitQty() == null
                || taskReqItem.getSubmitQty().compareTo(BigDecimal.ZERO) <= 0);
        if (UtilCollection.isEmpty(stockTaskReqItemDTOList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
        /* ************************ 作业请求合法性校验 **********************************/
        BizReceiptTaskReqHeadDTO reqHeadDTO = po.getStockTaskReqHeadInfo();
        Long headId = reqHeadDTO.getId();
        BizReceiptTaskReqHead bizReceiptTaskReqHead = bizReceiptTaskReqHeadDataWrap.getById(headId);
        if (null == bizReceiptTaskReqHead) {
            throw new WmsException(EnumReturnMsg.RETURN_REQ_IS_DELETE_NOT_SUBMIT);
        }
        if (EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().intValue() == bizReceiptTaskReqHead.getReceiptStatus()
                .intValue()) {
            throw new WmsException(EnumReturnMsg.RETURN_REQ_IS_COMPLETE_NOT_SUBMIT);
        }
        if (EnumRealYn.TRUE.getIntValue().equals(bizReceiptTaskReqHead.getIsDelete())) {
            throw new WmsException(EnumReturnMsg.RETURN_REQ_IS_DELETE_NOT_SUBMIT);
        }
        /* ************************ 行项目与仓位仓库是否一致 **********************************/
        Set<String> errorWhBinSet = new HashSet<>();
        Set<Long> errorBinSet = new HashSet<>();
        List<RecommendStockBinVO> errorDefaultBinList = new ArrayList<>();
        po.getStockTaskReqHeadInfo().getItemList().forEach(item -> {
            if (UtilCollection.isNotEmpty(item.getStockBinList())) {
                List<StockBinDTO> stockBinList = item.getStockBinList();
                stockBinList = stockBinList.stream().filter(i -> i.getOperationQty() != null && i.getOperationQty().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
                if (UtilCollection.isEmpty(stockBinList)) {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
                }
                item.setStockBinList(stockBinList);
                UtilCollection.toList(stockBinList, RecommendStockBinVO.class).forEach(bin -> {
                    // 获取bin信息
                    DicWhStorageBinDTO binDTO = dictionaryService.getBinCacheById(bin.getBinId());
                    // 空仓位
                    if (null == binDTO) {
                        errorBinSet.add(bin.getBinId());
                    } else {
                        // 行项目的仓库和提交仓位的仓库是否一致
                        if (!item.getWhId().equals(binDTO.getWhId())) {
                            errorWhBinSet.add(item.getRid());
                        }
                        // 提交仓位是否为默认仓位-根据存储类型是否为临时存储类型
                        if (UtilConst.getInstance().getDefaultStorageTypeCodeSet().contains(bin.getTypeCode())) {
                            errorDefaultBinList.add(bin);
                        }
                    }
                });
            }
        });
        if (UtilCollection.isNotEmpty(errorBinSet)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_BIN_ERROR, errorBinSet.toString());
        }
        if (UtilCollection.isNotEmpty(errorWhBinSet)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_ITEM_WH_NOT_EQUALS_BIN_WH, errorWhBinSet.toString());
        }
        if (UtilCollection.isNotEmpty(errorDefaultBinList)) {
            Set<String> whCodeSet = new HashSet<>(), typeCodeSet = new HashSet<>(), binCodeSet = new HashSet<>();
            errorDefaultBinList.forEach(bin -> {
                whCodeSet.add(bin.getWhCode());
                typeCodeSet.add(bin.getTypeCode());
                binCodeSet.add(bin.getBinCode());
            });
            throw new WmsException(EnumReturnMsg.RETURN_CODE_NOT_CONFIGURATION_BIN_CODE, whCodeSet.toString(),
                    typeCodeSet.toString(), binCodeSet.toString());
        }
    }

    /**
     * 上架请求根据物料-工厂-库存地推荐仓位
     * @param ctx
     */
    public void setRBinCode(BizContext ctx) {
        BizResultVO<BizReceiptTaskReqHeadDTO> headVo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        if (UtilObject.isNull(headVo.getHead()) || UtilCollection.isEmpty(headVo.getHead().getItemList())) {
            return;
        }
        List<Long> matIdList = headVo.getHead().getItemList().stream().map(BizReceiptTaskReqItemDTO::getMatId).collect(Collectors.toList());
        BizReceiptTaskReqItemDTO bizReceiptTaskReqItemDTO = headVo.getHead().getItemList().get(0);
        StockBinPO stockBinPo = new StockBinPO();
        stockBinPo.setMatIdList(matIdList);
        stockBinPo.setFtyId(bizReceiptTaskReqItemDTO.getFtyId());
        List<StockBinDTO> stockBinDTOList = stockCommonService.getStockBinByStockBinPo(stockBinPo);
        for (BizReceiptTaskReqItemDTO receiptTaskReqItemDTO : headVo.getHead().getItemList()) {
            List<StockBinDTO> stockBinDTOS = stockBinDTOList.stream().filter(stockBin -> stockBin.getMatId().equals(receiptTaskReqItemDTO.getMatId())
                    && stockBin.getFtyId().equals(receiptTaskReqItemDTO.getFtyId())
                    && stockBin.getLocationId().equals(receiptTaskReqItemDTO.getLocationId())
                    && UtilNumber.isEmpty(stockBin.getIsDefault())
            ).collect(Collectors.toList());
            if(UtilCollection.isEmpty(stockBinDTOS)){
                continue;
            }
            StockBinDTO stockBinDTO = stockBinDTOS.stream().sorted(Comparator.comparing(StockBinDTO::getCreateTime).reversed()).findFirst().orElse(null);
            if(UtilObject.isNull(stockBinDTO)){
                continue;
            }
            receiptTaskReqItemDTO.setRecommendBinId(stockBinDTO.getBinId());
            receiptTaskReqItemDTO.setStorageBin(dictionaryService.getBinCacheById(stockBinDTO.getBinId()));
        }

    }

    /**
     * 更新批次信息备注
     */
    public void updateBatchRemark(BizContext ctx) {
        // 请求参数
        BizReceiptTaskReqSavePo po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        BizReceiptTaskReqHeadDTO reqHead = po.getStockTaskReqHeadInfo();
        List<BizReceiptTaskReqItemDTO> completedReqItemList = reqHead.getItemList().stream().filter(item -> item.getItemStatus().equals(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue())).collect(Collectors.toList());
        if (UtilCollection.isEmpty(completedReqItemList)){
            return;
        }
        List<BizBatchInfo> batchInfoList = new ArrayList<>();
        for (BizReceiptTaskReqItemDTO bizReceiptTaskReqItemDTO : completedReqItemList) {
            BizBatchInfo batchInfo = new BizBatchInfo();
            batchInfo.setId(bizReceiptTaskReqItemDTO.getBatchId());
            batchInfo.setDescription(bizReceiptTaskReqItemDTO.getItemRemark());
            batchInfoList.add(batchInfo);
        }
        batchInfoDataWrap.updateBatchById(batchInfoList);
    }

    public void export(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("上架管理"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        // 上下文入参
        BizReceiptTaskSearchPo po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        po.setReceiptType(EnumReceiptType.STOCK_TASK_REQ_SHELF_LOAD.getValue());
        List<BizReceiptTaskItemExportVO> list = bizReceiptTaskItemDataWrap.selectExportItemList(po);
        dataFillService.fillAttr(list);

        UtilExcel.writeExcel(BizReceiptTaskItemExportVO.class, list, bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }


    private String getFileCode(String ext) {
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");
        if (ext == null || ext.trim().length() == 0) {
            return uuid;
        } else {
            return String.format("%s.%s", uuid, ext);
        }
    }

    private String getFileName(String fileName) {
        String yyyyMmDd = UtilDate.getStringDateForDate(new Date());
        return fileName + "-" + yyyyMmDd;
    }

    private String getLangCodeFromRequest() {
        ServletRequestAttributes ra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (ra == null) {
            return Const.DEFAULT_LANG_CODE;
        }
        HttpServletRequest request = ra.getRequest();
        String langCode = request.getHeader(Const.LANG_CODE_HEADER_NAME);
        return langCode == null ? Const.DEFAULT_LANG_CODE : langCode;
    }

    /**
     * 上架模板导入
     *
     * @param ctx
     */

    public void importLoadTaskReq(BizContext ctx, MultipartFile file) {
        try (InputStream inputStream = file.getInputStream()) {
            // 导入模板
            List<BizReceiptLoadTaskReqItemExportVO> importList = readExcel(inputStream);
            // 校验
            this.validateLoadImportData(importList);

            Map<String, List<BizReceiptLoadTaskReqItemExportVO>> listMap = importList.stream()
                    .collect(Collectors.groupingBy(BizReceiptLoadTaskReqItemExportVO::getRid));

            this.validateLoadItemData(listMap);
            // 构造参数
            this.processLoadTaskItems(ctx, listMap);
        } catch (IOException e) {
            log.error("导入失败，文件：{}", file.getOriginalFilename(), e);
            throw new WmsException(EnumReturnMsg.RETURN_CODE_IO_EXCEPTION);
        }
    }

    private List<BizReceiptLoadTaskReqItemExportVO> readExcel(InputStream inputStream) {
        try {
            return (List<BizReceiptLoadTaskReqItemExportVO>) UtilExcel.readExcelData(inputStream, BizReceiptLoadTaskReqItemExportVO.class, 1);
        } catch (ExcelAnalysisException e) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_IMOPRT_TEMPLATE_HAS_NO_DATA);
        } catch (Exception e) {
            log.error("Excel解析异常", e);
            if (e instanceof InterruptedException) {
                Thread.currentThread().interrupt();
            }
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EXCEPTION);
        }
    }

    private void validateLoadImportData(List<BizReceiptLoadTaskReqItemExportVO> importList) {
        int rowNum = 1;
        for (BizReceiptLoadTaskReqItemExportVO dto : importList) {
            // if (UtilNumber.isEmpty(dto.getOperationQty()) || UtilString.isNullOrEmpty(dto.getBinCodeRecommend())) {
            //     throw new WmsException(EnumReturnMsg.RETURN_CODE_EXCEL_REQUIRED_MSG, String.valueOf(rowNum));
            // }
            // if (dto.getOperationQty().compareTo(BigDecimal.ZERO) <= 0) {
            //     throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
            // }
            if (UtilNumber.isEmpty(dto.getOperationQty())) {
                dto.setOperationQty(BigDecimal.ZERO);
            }
            if (UtilString.isNotNullOrEmpty(dto.getBinCodeRecommend())) {
                if (Objects.isNull(dictionaryService.getBinCacheByCode(dto.getBinCodeRecommend()))) {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_BIN_ERROR);
                }
            }

            rowNum++;
        }
    }

    private void validateLoadItemData(Map<String, List<BizReceiptLoadTaskReqItemExportVO>> listMap) {
        // listMap.forEach((rid, list) -> {
        //     BigDecimal sumQty = list.stream()
        //             .map(BizReceiptLoadTaskReqItemExportVO::getOperationQty)
        //             .reduce(BigDecimal.ZERO, BigDecimal::add);
        //
        //     if (list.isEmpty() || sumQty.compareTo(list.get(0).getUnTaskQty()) > 0) {
        //         throw new WmsException(EnumReturnMsg.RETURN_CODE_QTY_GT_UNLOAD_QTY, rid);
        //     }
        // });
        listMap.forEach((k, v) -> {
            if (v.size() > 1) {
                throw new WmsException("行项目重复");
            }
            if (v.get(0).getUnTaskQty().compareTo(v.get(0).getOperationQty()) < 0) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_QTY_GT_LOAD_QTY, k);
            }
        });
    }

    private void processLoadTaskItems(BizContext ctx, Map<String, List<BizReceiptLoadTaskReqItemExportVO>> listMap) {

        BizResultVO<BizReceiptTaskVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        BizReceiptTaskVO headDTO = vo.getHead();
        List<BizReceiptTaskReqItemVO> taskReqItemDTOS = vo.getHead().getReqItemList();

        taskReqItemDTOS.forEach(item -> processLoadItem(listMap, item));

        headDTO.setReqItemList(taskReqItemDTOS);


        BizResultVO<BizReceiptTaskVO> bizResultVO = new BizResultVO<>();
        bizResultVO.setHead(headDTO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, bizResultVO);
    }

    private void processLoadItem(Map<String, List<BizReceiptLoadTaskReqItemExportVO>> listMap, BizReceiptTaskReqItemVO item) {
        Optional.ofNullable(listMap.get(item.getRid()))
                .filter(UtilCollection::isNotEmpty)
                .ifPresent(exportVos -> {
                    BigDecimal totalQty = exportVos.stream()
                            .map(BizReceiptLoadTaskReqItemExportVO::getOperationQty)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    // item.setQty(totalQty);
                    item.setSubmitQty(totalQty);
                    DicWhStorageBinDTO binCache = Optional.ofNullable(dictionaryService.getBinCacheByCode(exportVos.get(0).getBinCodeRecommend()))
                            .orElseThrow(() -> new WmsException(EnumReturnMsg.RETURN_CODE_STORAGE_BIN_NOT_EXIST));
                    item.setTypeId(binCache.getTypeId());
                    item.setTypeCode(binCache.getTypeCode());
                    item.setBinCode(binCache.getBinCode());
                    item.setStockBinList(createLoadStockBinList(exportVos));
                });
    }

    private List<RecommendStockBinVO> createLoadStockBinList(List<BizReceiptLoadTaskReqItemExportVO> exportVos) {
        return exportVos.stream().map(vo -> {

            DicWhStorageBinDTO binCache = Optional.ofNullable(dictionaryService.getBinCacheByCode(vo.getBinCodeRecommend()))
                    .orElseThrow(() -> new WmsException(EnumReturnMsg.RETURN_CODE_STORAGE_BIN_NOT_EXIST));

            return new RecommendStockBinVO()
                    .setTypeId(binCache.getTypeId())
                    .setWhCode(binCache.getWhCode())
                    .setOperationQty(vo.getOperationQty())
                    .setBinCode(vo.getBinCodeRecommend())
                    .setBinId(binCache.getId());
        }).collect(Collectors.toList());
    }

}
